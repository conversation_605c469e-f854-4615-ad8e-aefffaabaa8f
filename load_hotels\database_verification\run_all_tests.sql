-- ============================================================================
-- MASTER TEST RUNNER
-- Executes all database verification tests in sequence
-- ============================================================================

\echo '============================================================================'
\echo 'HOTEL DATABASE COMPREHENSIVE VERIFICATION SUITE'
\echo 'Running all verification tests...'
\echo '============================================================================'
\echo ''

-- Set output format for better readability
\pset border 2
\pset format aligned

-- Show current database and connection info
\echo 'Database Connection Information:'
SELECT 
    current_database() as database_name,
    current_user as connected_user,
    version() as postgresql_version,
    now() as test_start_time;

\echo ''
\echo 'Starting comprehensive verification...'
\echo ''

-- Test 1: Basic Counts and Statistics
\echo '============================================================================'
\echo 'RUNNING TEST SUITE 1: BASIC COUNTS AND STATISTICS'
\echo '============================================================================'
\i 01_basic_counts.sql

\echo ''
\echo ''

-- Test 2: Data Quality Checks
\echo '============================================================================'
\echo 'RUNNING TEST SUITE 2: DATA QUALITY CHECKS'
\echo '============================================================================'
\i 02_data_quality.sql

\echo ''
\echo ''

-- Test 3: Sample Data Verification
\echo '============================================================================'
\echo 'RUNNING TEST SUITE 3: SAMPLE DATA VERIFICATION'
\echo '============================================================================'
\i 03_sample_data.sql

\echo ''
\echo ''

-- Test 4: Duplicate Detection
\echo '============================================================================'
\echo 'RUNNING TEST SUITE 4: DUPLICATE DETECTION'
\echo '============================================================================'
\i 04_duplicate_detection.sql

\echo ''
\echo ''

-- Test 5: Performance Tests
\echo '============================================================================'
\echo 'RUNNING TEST SUITE 5: PERFORMANCE TESTS'
\echo '============================================================================'
\i 05_performance_tests.sql

\echo ''
\echo ''

-- Final Summary
\echo '============================================================================'
\echo 'COMPREHENSIVE VERIFICATION COMPLETED'
\echo '============================================================================'

-- Generate final summary report
\echo 'FINAL SUMMARY REPORT:'

SELECT 
    'Total Accommodations' as metric,
    COUNT(*)::text as value
FROM accommodation
UNION ALL
SELECT 
    'Accommodations with External ID' as metric,
    COUNT(CASE WHEN external_id IS NOT NULL THEN 1 END)::text as value
FROM accommodation
UNION ALL
SELECT 
    'Total Locations' as metric,
    COUNT(*)::text as value
FROM location
UNION ALL
SELECT 
    'Total Cities with Accommodations' as metric,
    COUNT(DISTINCT c.id)::text as value
FROM city c
JOIN location l ON c.id = l.city_id
JOIN accommodation a ON l.id = a.location_id
UNION ALL
SELECT 
    'Total Amenity Mappings' as metric,
    COUNT(*)::text as value
FROM accommodation_amenity_junction
UNION ALL
SELECT 
    'Total Reviews' as metric,
    COUNT(*)::text as value
FROM review
UNION ALL
SELECT 
    'Total Entity Images' as metric,
    COUNT(*)::text as value
FROM entity_image
ORDER BY metric;

\echo ''

-- Data Quality Summary
\echo 'DATA QUALITY SUMMARY:'
SELECT 
    'Accommodations with Phone' as metric,
    COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END)::text || 
    ' (' || ROUND(COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) * 100.0 / COUNT(*), 1)::text || '%)' as value
FROM accommodation
UNION ALL
SELECT 
    'Accommodations with Website' as metric,
    COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END)::text || 
    ' (' || ROUND(COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END) * 100.0 / COUNT(*), 1)::text || '%)' as value
FROM accommodation
UNION ALL
SELECT 
    'Locations with Address' as metric,
    COUNT(CASE WHEN address IS NOT NULL AND address != '' THEN 1 END)::text || 
    ' (' || ROUND(COUNT(CASE WHEN address IS NOT NULL AND address != '' THEN 1 END) * 100.0 / COUNT(*), 1)::text || '%)' as value
FROM location
UNION ALL
SELECT 
    'Locations with Coordinates' as metric,
    COUNT(CASE WHEN lat IS NOT NULL AND lng IS NOT NULL THEN 1 END)::text || 
    ' (' || ROUND(COUNT(CASE WHEN lat IS NOT NULL AND lng IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 1)::text || '%)' as value
FROM location
ORDER BY metric;

\echo ''

-- Test completion timestamp
SELECT 
    'Verification completed at: ' || now()::text as completion_info;

\echo ''
\echo 'All verification tests completed successfully!'
\echo 'Review the results above for any issues or anomalies.'
\echo ''
\echo 'For detailed analysis:'
\echo '  - Check count discrepancies in basic statistics'
\echo '  - Review any data quality violations'
\echo '  - Investigate duplicate records if found'
\echo '  - Monitor performance test timings'
\echo ''
\echo '============================================================================'
