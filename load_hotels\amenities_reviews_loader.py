#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Amenities & Reviews Loader
Refactored from Phase 4: Loads hotel amenities and review data.
"""

import os
import logging
import traceback
from typing import Dict, Optional, List
from datetime import datetime
try:
    from .database import DatabaseManager
    from .utils import FileManager, ProgressTracker, DataValidator
    from .config import HotelLoadingConfig
except ImportError:
    from database import DatabaseManager
    from utils import FileManager, ProgressTracker, DataValidator
    from config import HotelLoadingConfig

logger = logging.getLogger(__name__)

class AmenitiesReviewsLoader:
    """Amenities and reviews loader - refactored from Phase 4"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.stats = {
            'cities_processed': 0,
            'hotels_processed': 0,
            'amenities_processed': 0,
            'reviews_processed': 0,
            'amenity_mappings_created': 0,
            'errors': []
        }

    def get_or_create_amenity(self, amenity_name: str) -> Optional[int]:
        """Get existing amenity or create new one"""
        try:
            with self.db.get_cursor() as cursor:
                # Try to find existing amenity
                cursor.execute("SELECT id FROM amenity WHERE name = %s", (amenity_name,))
                result = cursor.fetchone()

                if result:
                    return result['id']

                # Create new amenity
                cursor.execute("""
                    INSERT INTO amenity (name, created_at, updated_at)
                    VALUES (%s, now(), now())
                    RETURNING id
                """, (amenity_name,))

                amenity_id = cursor.fetchone()['id']
                logger.debug(f"    ✓ Created new amenity: {amenity_name} (ID: {amenity_id})")
                return amenity_id

        except Exception as e:
            logger.error(f"    ✗ Amenity creation error for '{amenity_name}': {e}")
            return None

    def process_hotel_amenities(self, accommodation_id: int, amenities_data: List) -> int:
        """Process amenities for a hotel"""
        if not amenities_data:
            return 0

        created_count = 0

        try:
            with self.db.get_cursor() as cursor:
                for amenity_name in amenities_data:
                    if not amenity_name or not isinstance(amenity_name, str):
                        continue

                    amenity_name = amenity_name.strip()
                    if not amenity_name:
                        continue

                    # Get or create amenity
                    amenity_id = self.get_or_create_amenity(amenity_name)
                    if not amenity_id:
                        continue

                    # Create amenity mapping (with conflict handling)
                    try:
                        cursor.execute("""
                            INSERT INTO accommodation_amenity_junction (accommodation_id, amenity_id, created_at)
                            VALUES (%s, %s, now())
                            ON CONFLICT (accommodation_id, amenity_id) DO NOTHING
                        """, (accommodation_id, amenity_id))

                        if cursor.rowcount > 0:
                            created_count += 1

                    except Exception as e:
                        logger.warning(f"    ⚠ Amenity mapping error: {e}")

            if created_count > 0:
                logger.debug(f"    ✓ Created {created_count} amenity mappings")

            return created_count

        except Exception as e:
            logger.error(f"    ✗ Hotel amenities processing error: {e}")
            return 0

    def process_hotel_reviews(self, accommodation_id: int, reviews_data: List) -> int:
        """Process reviews for a hotel"""
        if not reviews_data:
            return 0

        created_count = 0

        try:
            with self.db.get_cursor() as cursor:
                for review in reviews_data:
                    if not isinstance(review, dict):
                        continue

                    try:
                        # Validate and clean review data
                        rating = DataValidator.validate_rating(review.get('rating'))
                        review_text = review.get('review_text', '').strip() if review.get('review_text') else None
                        reviewer_name = review.get('reviewer_name', '').strip() if review.get('reviewer_name') else None

                        # Skip reviews without essential data
                        if not rating and not review_text:
                            continue

                        cursor.execute("""
                            INSERT INTO review (
                                accommodation_id, rating, review_text, reviewer_name, created_at, updated_at
                            )
                            VALUES (%s, %s, %s, %s, now(), now())
                        """, (accommodation_id, rating, review_text, reviewer_name))

                        created_count += 1

                    except Exception as e:
                        logger.warning(f"    ⚠ Review creation error: {e}")

            if created_count > 0:
                logger.debug(f"    ✓ Created {created_count} reviews")

            return created_count

        except Exception as e:
            logger.error(f"    ✗ Hotel reviews processing error: {e}")
            return 0

    def process_single_hotel(self, external_id: str, city_id: int, city_key: str) -> Dict:
        """Process amenities and reviews for a single hotel"""
        result = {
            'success': False,
            'amenity_mappings_created': 0,
            'reviews_created': 0,
            'error': None
        }

        try:
            # Get accommodation ID by external ID
            accommodation_data = self.db.get_accommodation_by_external_id(external_id, city_id)
            if not accommodation_data:
                result['error'] = f"No accommodation found for external ID {external_id}"
                return result

            accommodation_id, location_id = accommodation_data

            # Load amenities data
            amenities_file = os.path.join(
                HotelLoadingConfig.get_data_file_path(city_key, 'amenities'),
                f"{external_id}.json"
            )

            if os.path.exists(amenities_file):
                amenities_data = FileManager.load_json_data(amenities_file)
                amenities_list = amenities_data.get('amenities', [])
                amenity_count = self.process_hotel_amenities(accommodation_id, amenities_list)
                result['amenity_mappings_created'] = amenity_count

            # Load reviews data
            reviews_file = os.path.join(
                HotelLoadingConfig.get_data_file_path(city_key, 'reviews'),
                f"{external_id}.json"
            )

            if os.path.exists(reviews_file):
                reviews_data = FileManager.load_json_data(reviews_file)
                reviews_list = reviews_data.get('reviews', [])
                review_count = self.process_hotel_reviews(accommodation_id, reviews_list)
                result['reviews_created'] = review_count

            result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"    ✗ Hotel processing error: {e}")
            logger.error(traceback.format_exc())

        return result

    def process_city(self, city_key: str, city_config: Dict) -> Dict:
        """Process amenities and reviews for all hotels in a city"""
        city_stats = {
            'city_name': city_config['city_name'],
            'hotels_found': 0,
            'hotels_processed': 0,
            'amenity_mappings_created': 0,
            'reviews_created': 0,
            'errors': [],
            'status': 'starting'
        }

        try:
            logger.info("="*60)
            logger.info(f"PROCESSING CITY: {city_config['city_name'].upper()}")
            logger.info("="*60)

            # Get list of hotels from database (those with external_id)
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT a.external_id
                    FROM accommodation a
                    JOIN location l ON a.location_id = l.id
                    WHERE l.city_id = %s AND a.external_id IS NOT NULL
                    ORDER BY a.external_id
                """, (city_config['city_id'],))

                hotels = [row['external_id'] for row in cursor.fetchall()]

            if not hotels:
                city_stats['errors'].append(f"No hotels with external_id found for {city_config['city_name']}")
                city_stats['status'] = 'failed'
                return city_stats

            city_stats['hotels_found'] = len(hotels)
            logger.info(f"Found {city_stats['hotels_found']} hotels in {city_config['city_name']}")

            # Initialize progress tracker
            progress = ProgressTracker(city_stats['hotels_found'])

            # Process each hotel
            for hotel_index, external_id in enumerate(hotels):
                try:
                    logger.info(f"Processing hotel {hotel_index + 1}/{city_stats['hotels_found']}: {external_id}")

                    # Process this hotel
                    result = self.process_single_hotel(external_id, city_config['city_id'], city_key)

                    if result['success']:
                        # Commit this individual hotel
                        self.db.commit()
                        city_stats['hotels_processed'] += 1
                        city_stats['amenity_mappings_created'] += result['amenity_mappings_created']
                        city_stats['reviews_created'] += result['reviews_created']
                        logger.info(f"✓ Hotel processed and committed successfully")
                    else:
                        # Rollback this individual hotel but continue with others
                        self.db.rollback()
                        city_stats['errors'].append(f"Hotel {hotel_index + 1}: {result['error']}")
                        logger.error(f"✗ Hotel processing failed: {result['error']}")

                    progress.update()

                except Exception as e:
                    # Rollback this individual hotel but continue with others
                    self.db.rollback()
                    error_msg = f"Hotel {hotel_index + 1} processing error: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    city_stats['errors'].append(error_msg)

            city_stats['status'] = 'completed'
            logger.info(f"✓ {city_config['city_name']} processing completed")
            logger.info(f"  Hotels processed: {city_stats['hotels_processed']}/{city_stats['hotels_found']}")
            logger.info(f"  Amenity mappings: {city_stats['amenity_mappings_created']}")
            logger.info(f"  Reviews: {city_stats['reviews_created']}")
            logger.info(f"  Errors: {len(city_stats['errors'])}")

        except Exception as e:
            # City-level error
            error_msg = f"City {city_config['city_name']} processing failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            city_stats['errors'].append(error_msg)
            city_stats['status'] = 'failed'

        return city_stats

    def load_amenities_reviews(self, target_cities: Dict) -> Dict:
        """Load amenities and reviews for all target cities"""
        logger.info("🚀 STARTING AMENITIES & REVIEWS LOADING")
        logger.info("="*80)

        start_time = datetime.now()
        results = {'cities': {}, 'overall': self.stats.copy()}

        try:
            # Process each target city
            for city_key, city_config in target_cities.items():
                city_result = self.process_city(city_key, city_config)
                results['cities'][city_key] = city_result

                # Update overall statistics
                if city_result['status'] == 'completed':
                    self.stats['cities_processed'] += 1
                    self.stats['hotels_processed'] += city_result['hotels_processed']
                    self.stats['amenity_mappings_created'] += city_result['amenity_mappings_created']
                    self.stats['reviews_processed'] += city_result['reviews_created']

                self.stats['errors'].extend(city_result['errors'])

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("\n" + "="*80)
            logger.info("🎉 AMENITIES & REVIEWS LOADING COMPLETED")
            logger.info("="*80)
            logger.info(f"⏱️  Duration: {duration}")
            logger.info(f"🏙️  Cities: {self.stats['cities_processed']}/{len(target_cities)}")
            logger.info(f"🏨 Hotels: {self.stats['hotels_processed']}")
            logger.info(f"🛎️  Amenity Mappings: {self.stats['amenity_mappings_created']}")
            logger.info(f"⭐ Reviews: {self.stats['reviews_processed']}")
            logger.info(f"❌ Errors: {len(self.stats['errors'])}")

            results['overall'] = self.stats
            return results

        except Exception as e:
            logger.error(f"❌ Amenities & reviews loading failed: {e}")
            logger.error(traceback.format_exc())
            results['overall']['errors'].append(str(e))
            return results
