#!/usr/bin/env python3
"""
Test script to verify the Unicode and file path fixes
"""

import os
import sys
import logging

# Add load_hotels to path
sys.path.insert(0, 'load_hotels')

def test_unicode_logging():
    """Test Unicode logging with safe replacements"""
    print("Testing Unicode logging fixes...")
    
    try:
        from load_hotels.utils import LoggingSetup
        
        # Test safe Unicode message conversion
        test_messages = [
            "✅ Test successful",
            "❌ Test failed", 
            "🔍 Searching files",
            "🚀 Starting process",
            "⏭️ Skipping step"
        ]
        
        for msg in test_messages:
            safe_msg = LoggingSetup.safe_unicode_message(msg)
            print(f"Original: {repr(msg)}")
            print(f"Safe:     {safe_msg}")
            print()
        
        # Test logging setup
        logger = LoggingSetup.setup_logging('test.log', 'INFO')
        logger.info("Test logging with Unicode: ✅ ❌ 🔍")
        
        print("✅ Unicode logging test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Unicode logging test failed: {e}")
        return False

def test_file_paths():
    """Test file path resolution"""
    print("Testing file path fixes...")
    
    try:
        from load_hotels.config import HotelLoadingConfig
        
        print(f"Current directory: {os.getcwd()}")
        print(f"BASE_DATA_PATH: {HotelLoadingConfig.BASE_DATA_PATH}")
        print(f"Absolute path: {os.path.abspath(HotelLoadingConfig.BASE_DATA_PATH)}")
        
        # Test paths for agadir
        city_key = 'agadir'
        paths = {
            'core': HotelLoadingConfig.get_data_file_path(city_key, 'core'),
            'amenities': HotelLoadingConfig.get_data_file_path(city_key, 'amenities'),
            'reviews': HotelLoadingConfig.get_data_file_path(city_key, 'reviews'),
            'contacts': HotelLoadingConfig.get_data_file_path(city_key, 'contacts')
        }
        
        print(f"\nFile paths for {city_key}:")
        for data_type, path in paths.items():
            exists = os.path.exists(path)
            is_dir = os.path.isdir(path) if exists else False
            status = "[OK]" if exists else "[MISSING]"
            type_info = "(dir)" if is_dir else "(file)" if exists else ""
            print(f"  {status} {data_type}: {path} {type_info}")
        
        # Test validation
        print(f"\nValidation results:")
        validation_results = HotelLoadingConfig.validate_data_files()
        
        for city_key, city_results in validation_results.items():
            print(f"  {city_key}:")
            required_types = HotelLoadingConfig.get_required_data_types()
            optional_types = HotelLoadingConfig.get_optional_data_types()
            
            for data_type, exists in city_results.items():
                if exists:
                    status = "[OK]"
                elif data_type in optional_types:
                    status = "[OPTIONAL]"
                else:
                    status = "[MISSING]"
                
                print(f"    {status} {data_type}")
        
        print("✅ File path test completed!")
        return True
        
    except Exception as e:
        print(f"❌ File path test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Hotel Loading Fixes")
    print("=" * 50)
    
    success = True
    
    # Test 1: Unicode logging
    if not test_unicode_logging():
        success = False
    
    print("-" * 50)
    
    # Test 2: File paths
    if not test_file_paths():
        success = False
    
    print("=" * 50)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
