#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Configuration
Centralized configuration for all hotel data loading phases.
"""

import os
from typing import Dict, List

class HotelLoadingConfig:
    """Centralized configuration for hotel data loading system"""

    # Database Configuration
    DATABASE_CONFIG = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }

    # Data Paths Configuration
    BASE_DATA_PATH = "../processed_data"
    CONTACTS_DATA_PATH = "../processed_data/contacts"  # Note: contacts data doesn't exist yet
    AMENITIES_DATA_PATH = "../processed_data/amenities"  # Fixed: amenities are in processed_data
    REVIEWS_DATA_PATH = "../processed_data/reviews"      # Fixed: reviews are in processed_data

    # City Configuration - Easy to extend for new cities
    # Format: 'city_key': {'city_name': 'Display Name', 'city_id': database_id, 'data_file': 'filename.json'}
    TARGET_CITIES = {
        # 'casablanca': {
        #     'city_name': 'Casablanca',
        #     'city_id': 4,  # Will be auto-loaded from database
        #     'data_file': 'casablanca.json'
        # },
        # 'marrakech': {
        #     'city_name': 'Marrakech',
        #     'city_id': 7,  # Will be auto-loaded from database
        #     'data_file': 'marrakech.json'
        # }
        # To add new cities, simply add entries here:
        'agadir': {
            'city_name': 'Agadir',
            'city_id': 3,  # Will be auto-loaded
            'data_file': 'agadir.json'
        }
    }

    # Processing Configuration
    BATCH_SIZE = 50  # Progress reporting interval
    MAX_RETRIES = 3  # Maximum retries for failed operations

    # Logging Configuration
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

    # Phase Configuration
    PHASES = {
        'core_data': {
            'name': 'Core Hotel Data',
            'description': 'Load basic hotel information, locations, price forecasts, and images',
            'enabled': True
        },
        'amenities_reviews': {
            'name': 'Amenities & Reviews',
            'description': 'Load hotel amenities and review data',
            'enabled': True
        },
        'contacts': {
            'name': 'Contact Information',
            'description': 'Load phone numbers, websites, and addresses',
            'enabled': True
        }
    }

    @classmethod
    def get_city_config(cls, city_key: str) -> Dict:
        """Get configuration for a specific city"""
        if city_key not in cls.TARGET_CITIES:
            raise ValueError(f"City '{city_key}' not found in configuration")
        return cls.TARGET_CITIES[city_key].copy()

    @classmethod
    def get_all_cities(cls) -> List[str]:
        """Get list of all configured city keys"""
        return list(cls.TARGET_CITIES.keys())

    @classmethod
    def get_data_file_path(cls, city_key: str, data_type: str = 'core') -> str:
        """Get full path to data file for a city"""
        city_config = cls.get_city_config(city_key)

        if data_type == 'core':
            return os.path.join(cls.BASE_DATA_PATH, city_config['data_file'])
        elif data_type == 'contacts':
            return os.path.join(cls.CONTACTS_DATA_PATH, city_config['data_file'])
        elif data_type == 'amenities':
            return os.path.join(cls.AMENITIES_DATA_PATH, city_key)
        elif data_type == 'reviews':
            return os.path.join(cls.REVIEWS_DATA_PATH, city_key)
        else:
            raise ValueError(f"Unknown data type: {data_type}")

    @classmethod
    def validate_data_files(cls) -> Dict[str, Dict[str, bool]]:
        """Validate that all required data files exist"""
        validation_results = {}

        for city_key in cls.get_all_cities():
            city_results = {}

            # Check core data file (required)
            core_path = cls.get_data_file_path(city_key, 'core')
            city_results['core_data'] = os.path.exists(core_path)

            # Check contacts data file (optional - may not exist yet)
            contacts_path = cls.get_data_file_path(city_key, 'contacts')
            city_results['contacts'] = os.path.exists(contacts_path)

            # Check amenities directory (required)
            amenities_path = cls.get_data_file_path(city_key, 'amenities')
            city_results['amenities'] = os.path.exists(amenities_path) and os.path.isdir(amenities_path)

            # Check reviews directory (required)
            reviews_path = cls.get_data_file_path(city_key, 'reviews')
            city_results['reviews'] = os.path.exists(reviews_path) and os.path.isdir(reviews_path)

            validation_results[city_key] = city_results

        return validation_results

    @classmethod
    def get_required_data_types(cls) -> List[str]:
        """Get list of required data types (contacts is optional)"""
        return ['core_data', 'amenities', 'reviews']

    @classmethod
    def get_optional_data_types(cls) -> List[str]:
        """Get list of optional data types"""
        return ['contacts']
