#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System

A clean, orchestrated solution for loading hotel data through multiple phases:
1. Core Data Loading (hotels, locations, price forecasts, images)
2. Amenities & Reviews Loading
3. Contact Information Loading

Features:
- Centralized configuration
- Comprehensive error handling
- Rollback mechanisms
- Progress reporting
- Easy city configuration
- Single command execution

Usage:
    python load_main.py

Configuration:
    Edit config.py to add new cities or modify settings.
"""

__version__ = "1.0.0"
__author__ = "Hotel Data Loading System"
__description__ = "Consolidated hotel data loading system with orchestrated phases"

# Import main components for easy access
from .config import HotelLoadingConfig
from .database import DatabaseManager
from .core_data_loader import CoreDataLoader
from .amenities_reviews_loader import AmenitiesReviewsLoader
from .contacts_loader import ContactsLoader
from .load_main import HotelDataOrchestrator

__all__ = [
    'HotelLoadingConfig',
    'DatabaseManager',
    'CoreDataLoader',
    'AmenitiesReviewsLoader',
    'ContactsLoader',
    'HotelDataOrchestrator'
]
