-- =====================================================
-- CHECK-IN/CHECK-OUT TIME ANALYSIS
-- Analyze whether these fields are needed in accommodation table
-- =====================================================

-- QUERY 1: Check current accommodation table structure
SELECT 'Current Accommodation Table Structure' as analysis_type;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'accommodation' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY ordinal_position;

-- QUERY 2: Check if any accommodations currently have check-in/check-out times
SELECT 'Current Check-in/Check-out Data' as analysis_type;
SELECT 
    COUNT(*) as total_accommodations,
    COUNT(check_in_time) as accommodations_with_checkin,
    COUNT(check_out_time) as accommodations_with_checkout,
    COUNT(CASE WHEN check_in_time IS NOT NULL AND check_out_time IS NOT NULL THEN 1 END) as accommodations_with_both
FROM accommodation;

-- QUERY 3: Sample of existing check-in/check-out data (if any)
SELECT 'Sample Check-in/Check-out Times' as analysis_type;
SELECT 
    id,
    name,
    check_in_time,
    check_out_time
FROM accommodation 
WHERE check_in_time IS NOT NULL OR check_out_time IS NOT NULL
LIMIT 10;

-- QUERY 4: Check trip_accommodation table structure (user-specific booking times)
SELECT 'Trip Accommodation Table Structure' as analysis_type;
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'trip_accommodation' 
AND column_name IN ('check_in_date', 'check_out_date')
ORDER BY ordinal_position;

-- QUERY 5: Analysis of trip_accommodation vs accommodation check-in/out fields
SELECT 'Field Usage Analysis' as analysis_type;
SELECT 
    'trip_accommodation has user-specific check-in/check-out DATES' as observation_1,
    'accommodation has general check-in/check-out TIMES (like 3:00 PM)' as observation_2,
    'These serve different purposes' as observation_3;

-- =====================================================
-- RECOMMENDATION ANALYSIS
-- =====================================================

SELECT 'FIELD PURPOSE ANALYSIS' as section;
SELECT 
    'accommodation.check_in_time' as field,
    'General hotel policy (e.g., 3:00 PM)' as purpose,
    'Used for displaying hotel information' as use_case,
    'NOT user-specific' as note
UNION ALL
SELECT 
    'accommodation.check_out_time' as field,
    'General hotel policy (e.g., 11:00 AM)' as purpose,
    'Used for displaying hotel information' as use_case,
    'NOT user-specific' as note
UNION ALL
SELECT 
    'trip_accommodation.check_in_date' as field,
    'User-specific booking dates' as purpose,
    'Used for trip planning and bookings' as use_case,
    'User-specific' as note
UNION ALL
SELECT 
    'trip_accommodation.check_out_date' as field,
    'User-specific booking dates' as purpose,
    'Used for trip planning and bookings' as use_case,
    'User-specific' as note;

-- =====================================================
-- RECOMMENDATION
-- =====================================================

SELECT 'RECOMMENDATION' as section;
SELECT 
    'KEEP the check_in_time and check_out_time fields in accommodation table' as recommendation,
    'These represent HOTEL POLICIES (general times like 3:00 PM check-in)' as reason_1,
    'Different from trip_accommodation which has USER-SPECIFIC dates' as reason_2,
    'Useful for displaying hotel information to users' as reason_3,
    'Example: "Check-in: 3:00 PM, Check-out: 11:00 AM"' as example;

-- =====================================================
-- OPTIONAL: POPULATE WITH STANDARD TIMES
-- =====================================================

SELECT 'OPTIONAL ENHANCEMENT' as section;
SELECT 
    'You could populate these fields with standard hotel times' as suggestion,
    'Most hotels: check_in_time = 15:00:00, check_out_time = 11:00:00' as standard_times,
    'This would provide useful information for users' as benefit;

-- Uncomment below to set standard check-in/check-out times for all hotels
/*
-- Set standard check-in/check-out times for hotels that don't have them
UPDATE accommodation 
SET 
    check_in_time = '15:00:00',  -- 3:00 PM
    check_out_time = '11:00:00', -- 11:00 AM
    updated_at = now()
WHERE check_in_time IS NULL OR check_out_time IS NULL;

-- Verify the update
SELECT 
    'After Update' as status,
    COUNT(*) as total_accommodations,
    COUNT(check_in_time) as accommodations_with_checkin,
    COUNT(check_out_time) as accommodations_with_checkout
FROM accommodation;
*/

-- =====================================================
-- SUMMARY
-- =====================================================

SELECT 'SUMMARY' as section;
SELECT 
    'accommodation.check_in_time/check_out_time = Hotel policies (times)' as field_1,
    'trip_accommodation.check_in_date/check_out_date = User bookings (dates)' as field_2,
    'Both are needed and serve different purposes' as conclusion,
    'Do NOT drop these fields from accommodation table' as final_recommendation;
