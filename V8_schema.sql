--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: accommodation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accommodation (
    id bigint NOT NULL,
    name character varying(200) NOT NULL,
    location_id bigint NOT NULL,
    type_id bigint NOT NULL,
    stars integer,
    average_score numeric(3,1),
    description text,
    check_in_time time without time zone,
    check_out_time time without time zone,
    website text,
    phone character varying(50),
    email character varying(255),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    construction_year integer,
    highlights text[],
    external_id character varying(50),
    CONSTRAINT accommodation_average_score_check CHECK (((average_score >= (0)::numeric) AND (average_score <= (10)::numeric))),
    CONSTRAINT accommodation_stars_check CHECK (((stars IS NULL) OR ((stars >= 1) AND (stars <= 5))))
);


ALTER TABLE public.accommodation OWNER TO postgres;

--
-- Name: COLUMN accommodation.external_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.accommodation.external_id IS 'External ID from source system (Trivago, TripAdvisor, etc.)';


--
-- Name: accommodation_amenity_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accommodation_amenity_junction (
    id bigint NOT NULL,
    accommodation_id bigint NOT NULL,
    amenity_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    is_free boolean,
    is_available boolean,
    is_top_amenity boolean DEFAULT false
);


ALTER TABLE public.accommodation_amenity_junction OWNER TO postgres;

--
-- Name: accommodation_amenity_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accommodation_amenity_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accommodation_amenity_junction_id_seq OWNER TO postgres;

--
-- Name: accommodation_amenity_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accommodation_amenity_junction_id_seq OWNED BY public.accommodation_amenity_junction.id;


--
-- Name: accommodation_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accommodation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accommodation_id_seq OWNER TO postgres;

--
-- Name: accommodation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accommodation_id_seq OWNED BY public.accommodation.id;


--
-- Name: accommodation_price_forecast_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accommodation_price_forecast_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accommodation_price_forecast_id_seq OWNER TO postgres;

--
-- Name: accommodation_price_forecast; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accommodation_price_forecast (
    id bigint DEFAULT nextval('public.accommodation_price_forecast_id_seq'::regclass) NOT NULL,
    accommodation_id bigint NOT NULL,
    forecast_month character varying(7) NOT NULL,
    price_value numeric(12,2) NOT NULL,
    currency character(3) DEFAULT 'MAD'::bpchar NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.accommodation_price_forecast OWNER TO postgres;

--
-- Name: accommodation_rate; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accommodation_rate (
    id bigint NOT NULL,
    accommodation_id bigint NOT NULL,
    room_id bigint NOT NULL,
    date_from date NOT NULL,
    date_to date NOT NULL,
    base_price numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    breakfast_included boolean DEFAULT false NOT NULL,
    refundable boolean DEFAULT true NOT NULL,
    cancellation_policy text,
    availability integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.accommodation_rate OWNER TO postgres;

--
-- Name: accommodation_rate_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accommodation_rate_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accommodation_rate_id_seq OWNER TO postgres;

--
-- Name: accommodation_rate_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accommodation_rate_id_seq OWNED BY public.accommodation_rate.id;


--
-- Name: accommodation_room; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accommodation_room (
    id bigint NOT NULL,
    accommodation_id bigint NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    max_guests integer NOT NULL,
    beds_description text,
    size_sqm integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.accommodation_room OWNER TO postgres;

--
-- Name: accommodation_room_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accommodation_room_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accommodation_room_id_seq OWNER TO postgres;

--
-- Name: accommodation_room_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accommodation_room_id_seq OWNED BY public.accommodation_room.id;


--
-- Name: accommodation_type; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accommodation_type (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    description text,
    icon_id bigint,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.accommodation_type OWNER TO postgres;

--
-- Name: accommodation_type_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accommodation_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accommodation_type_id_seq OWNER TO postgres;

--
-- Name: accommodation_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accommodation_type_id_seq OWNED BY public.accommodation_type.id;


--
-- Name: activity; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.activity (
    id bigint NOT NULL,
    name character varying(200) NOT NULL,
    location_id bigint NOT NULL,
    category_id bigint NOT NULL,
    description text NOT NULL,
    duration_minutes integer NOT NULL,
    price_adult numeric(10,2),
    price_child numeric(10,2),
    currency character(3),
    website text,
    phone character varying(20),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.activity OWNER TO postgres;

--
-- Name: activity_category; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.activity_category (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    icon_id bigint,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.activity_category OWNER TO postgres;

--
-- Name: activity_category_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.activity_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.activity_category_id_seq OWNER TO postgres;

--
-- Name: activity_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.activity_category_id_seq OWNED BY public.activity_category.id;


--
-- Name: activity_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.activity_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.activity_id_seq OWNER TO postgres;

--
-- Name: activity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.activity_id_seq OWNED BY public.activity.id;


--
-- Name: activity_opening_hours; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.activity_opening_hours (
    id bigint NOT NULL,
    activity_id bigint NOT NULL,
    day_of_week integer NOT NULL,
    opening_time time without time zone,
    closing_time time without time zone,
    is_closed boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT activity_opening_hours_day_of_week_check CHECK (((day_of_week >= 0) AND (day_of_week <= 6)))
);


ALTER TABLE public.activity_opening_hours OWNER TO postgres;

--
-- Name: activity_opening_hours_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.activity_opening_hours_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.activity_opening_hours_id_seq OWNER TO postgres;

--
-- Name: activity_opening_hours_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.activity_opening_hours_id_seq OWNED BY public.activity_opening_hours.id;


--
-- Name: activity_tag_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.activity_tag_junction (
    id bigint NOT NULL,
    activity_id bigint NOT NULL,
    tag_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.activity_tag_junction OWNER TO postgres;

--
-- Name: activity_tag_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.activity_tag_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.activity_tag_junction_id_seq OWNER TO postgres;

--
-- Name: activity_tag_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.activity_tag_junction_id_seq OWNED BY public.activity_tag_junction.id;


--
-- Name: amenity; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.amenity (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    category character varying(50) NOT NULL,
    icon_id bigint,
    description text,
    amenity_type character varying(20) DEFAULT 'both'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT amenity_amenity_type_check CHECK (((amenity_type)::text = ANY (ARRAY[('accommodation'::character varying)::text, ('restaurant'::character varying)::text, ('both'::character varying)::text])))
);


ALTER TABLE public.amenity OWNER TO postgres;

--
-- Name: amenity_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.amenity_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.amenity_id_seq OWNER TO postgres;

--
-- Name: amenity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.amenity_id_seq OWNED BY public.amenity.id;


--
-- Name: budget_allocation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.budget_allocation (
    id bigint NOT NULL,
    trip_id bigint NOT NULL,
    category_id bigint NOT NULL,
    allocated_amount numeric(12,2) NOT NULL,
    spent_amount numeric(12,2) DEFAULT 0 NOT NULL,
    currency character(3) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.budget_allocation OWNER TO postgres;

--
-- Name: budget_allocation_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.budget_allocation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.budget_allocation_id_seq OWNER TO postgres;

--
-- Name: budget_allocation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.budget_allocation_id_seq OWNED BY public.budget_allocation.id;


--
-- Name: budget_category; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.budget_category (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    icon_id bigint,
    description text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.budget_category OWNER TO postgres;

--
-- Name: budget_category_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.budget_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.budget_category_id_seq OWNER TO postgres;

--
-- Name: budget_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.budget_category_id_seq OWNED BY public.budget_category.id;


--
-- Name: car_rental; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.car_rental (
    id bigint NOT NULL,
    provider_id bigint NOT NULL,
    city_id bigint NOT NULL,
    location_id bigint NOT NULL,
    car_type text NOT NULL,
    daily_rate numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    insurance_cost numeric(12,2),
    is_airport_pickup boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.car_rental OWNER TO postgres;

--
-- Name: car_rental_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.car_rental_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.car_rental_id_seq OWNER TO postgres;

--
-- Name: car_rental_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.car_rental_id_seq OWNED BY public.car_rental.id;


--
-- Name: city; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.city (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    country_id bigint NOT NULL,
    timezone character varying(50) NOT NULL,
    short_description text,
    about text,
    best_time_to_visit text,
    average_budget_min numeric(12,2),
    average_budget_max numeric(12,2),
    recommended_stay_min integer,
    recommended_stay_max integer,
    video_url text,
    map_embed_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.city OWNER TO postgres;

--
-- Name: city_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.city_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.city_id_seq OWNER TO postgres;

--
-- Name: city_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.city_id_seq OWNED BY public.city.id;


--
-- Name: country; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.country (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    code character(2),
    currency character(3) NOT NULL,
    language character varying(50),
    visa_requirements text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.country OWNER TO postgres;

--
-- Name: country_custom; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.country_custom (
    id bigint NOT NULL,
    country_id bigint NOT NULL,
    title character varying(200) NOT NULL,
    description text NOT NULL,
    icon_id bigint,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.country_custom OWNER TO postgres;

--
-- Name: country_custom_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.country_custom_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_custom_id_seq OWNER TO postgres;

--
-- Name: country_custom_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.country_custom_id_seq OWNED BY public.country_custom.id;


--
-- Name: country_emergency_contact; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.country_emergency_contact (
    id bigint NOT NULL,
    country_id bigint NOT NULL,
    service_type character varying(50) NOT NULL,
    phone_number character varying(20) NOT NULL,
    notes text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.country_emergency_contact OWNER TO postgres;

--
-- Name: country_emergency_contact_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.country_emergency_contact_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_emergency_contact_id_seq OWNER TO postgres;

--
-- Name: country_emergency_contact_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.country_emergency_contact_id_seq OWNED BY public.country_emergency_contact.id;


--
-- Name: country_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.country_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_id_seq OWNER TO postgres;

--
-- Name: country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.country_id_seq OWNED BY public.country.id;


--
-- Name: cuisine_type; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cuisine_type (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    description text,
    icon_id bigint,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.cuisine_type OWNER TO postgres;

--
-- Name: cuisine_type_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cuisine_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cuisine_type_id_seq OWNER TO postgres;

--
-- Name: cuisine_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cuisine_type_id_seq OWNED BY public.cuisine_type.id;


--
-- Name: daily_itinerary; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.daily_itinerary (
    id bigint NOT NULL,
    trip_day_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.daily_itinerary OWNER TO postgres;

--
-- Name: daily_itinerary_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.daily_itinerary_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.daily_itinerary_id_seq OWNER TO postgres;

--
-- Name: daily_itinerary_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.daily_itinerary_id_seq OWNED BY public.daily_itinerary.id;


--
-- Name: daily_itinerary_item; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.daily_itinerary_item (
    id bigint NOT NULL,
    daily_itinerary_id bigint NOT NULL,
    sequence integer NOT NULL,
    item_type character varying(20) NOT NULL,
    item_id bigint,
    start_time time without time zone,
    end_time time without time zone,
    notes text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT daily_itinerary_item_item_type_check CHECK (((item_type)::text = ANY (ARRAY[('activity'::character varying)::text, ('meal'::character varying)::text, ('transport'::character varying)::text, ('free_time'::character varying)::text, ('guided_tour'::character varying)::text])))
);


ALTER TABLE public.daily_itinerary_item OWNER TO postgres;

--
-- Name: daily_itinerary_item_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.daily_itinerary_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.daily_itinerary_item_id_seq OWNER TO postgres;

--
-- Name: daily_itinerary_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.daily_itinerary_item_id_seq OWNED BY public.daily_itinerary_item.id;


--
-- Name: entity_image; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entity_image (
    id bigint NOT NULL,
    image_id bigint NOT NULL,
    entity_type character varying(50) NOT NULL,
    entity_id bigint NOT NULL,
    image_type character varying(50) DEFAULT 'gallery'::character varying NOT NULL,
    sequence integer DEFAULT 0,
    caption text,
    is_featured boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT entity_image_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('accommodation'::character varying)::text, ('activity'::character varying)::text, ('restaurant'::character varying)::text, ('tour'::character varying)::text, ('city'::character varying)::text, ('user'::character varying)::text, ('trip'::character varying)::text, ('room'::character varying)::text, ('provider'::character varying)::text]))),
    CONSTRAINT entity_image_image_type_check CHECK (((image_type)::text = ANY (ARRAY[('featured'::character varying)::text, ('gallery'::character varying)::text, ('thumbnail'::character varying)::text, ('hero'::character varying)::text, ('avatar'::character varying)::text, ('logo'::character varying)::text])))
);


ALTER TABLE public.entity_image OWNER TO postgres;

--
-- Name: entity_image_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entity_image_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entity_image_id_seq OWNER TO postgres;

--
-- Name: entity_image_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entity_image_id_seq OWNED BY public.entity_image.id;


--
-- Name: expense; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.expense (
    id bigint NOT NULL,
    trip_id bigint NOT NULL,
    trip_day_id bigint,
    category_id bigint NOT NULL,
    amount numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    description text,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    payment_method character varying(50),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.expense OWNER TO postgres;

--
-- Name: expense_allocation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.expense_allocation (
    expense_id bigint NOT NULL,
    entity_type character varying(20) NOT NULL,
    entity_id bigint NOT NULL,
    amount numeric(12,2) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT expense_allocation_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('accommodation'::character varying)::text, ('activity'::character varying)::text, ('meal'::character varying)::text, ('transport'::character varying)::text, ('other'::character varying)::text])))
);


ALTER TABLE public.expense_allocation OWNER TO postgres;

--
-- Name: expense_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.expense_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.expense_id_seq OWNER TO postgres;

--
-- Name: expense_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.expense_id_seq OWNED BY public.expense.id;


--
-- Name: guide_availability; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guide_availability (
    id bigint NOT NULL,
    guide_id bigint NOT NULL,
    date date NOT NULL,
    available_from time without time zone,
    available_to time without time zone,
    is_full_day boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.guide_availability OWNER TO postgres;

--
-- Name: guide_availability_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guide_availability_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guide_availability_id_seq OWNER TO postgres;

--
-- Name: guide_availability_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guide_availability_id_seq OWNED BY public.guide_availability.id;


--
-- Name: guide_language_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guide_language_junction (
    id bigint NOT NULL,
    guide_id bigint NOT NULL,
    language_id bigint NOT NULL,
    proficiency character varying(20) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT guide_language_junction_proficiency_check CHECK (((proficiency)::text = ANY (ARRAY[('basic'::character varying)::text, ('conversational'::character varying)::text, ('fluent'::character varying)::text, ('native'::character varying)::text])))
);


ALTER TABLE public.guide_language_junction OWNER TO postgres;

--
-- Name: guide_language_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guide_language_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guide_language_junction_id_seq OWNER TO postgres;

--
-- Name: guide_language_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guide_language_junction_id_seq OWNED BY public.guide_language_junction.id;


--
-- Name: guide_location_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guide_location_junction (
    id bigint NOT NULL,
    guide_id bigint NOT NULL,
    city_id bigint NOT NULL,
    is_primary boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.guide_location_junction OWNER TO postgres;

--
-- Name: guide_location_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guide_location_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guide_location_junction_id_seq OWNER TO postgres;

--
-- Name: guide_location_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guide_location_junction_id_seq OWNED BY public.guide_location_junction.id;


--
-- Name: guide_profile; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guide_profile (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    bio text NOT NULL,
    specialization character varying(200) NOT NULL,
    experience_years integer NOT NULL,
    hourly_rate numeric(10,2) NOT NULL,
    full_day_rate numeric(10,2) NOT NULL,
    category character varying(100),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.guide_profile OWNER TO postgres;

--
-- Name: guide_profile_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guide_profile_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guide_profile_id_seq OWNER TO postgres;

--
-- Name: guide_profile_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guide_profile_id_seq OWNED BY public.guide_profile.id;


--
-- Name: guide_request; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guide_request (
    id bigint NOT NULL,
    traveler_id uuid NOT NULL,
    guide_id bigint NOT NULL,
    trip_id bigint,
    tour_id bigint,
    title character varying(200) NOT NULL,
    description text,
    date date NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    travelers_count integer DEFAULT 1 NOT NULL,
    amount numeric(12,2) NOT NULL,
    currency character(3) DEFAULT 'USD'::bpchar NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT guide_request_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('accepted'::character varying)::text, ('rejected'::character varying)::text, ('completed'::character varying)::text, ('cancelled'::character varying)::text])))
);


ALTER TABLE public.guide_request OWNER TO postgres;

--
-- Name: guide_request_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guide_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guide_request_id_seq OWNER TO postgres;

--
-- Name: guide_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guide_request_id_seq OWNED BY public.guide_request.id;


--
-- Name: guide_specialty; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guide_specialty (
    id bigint NOT NULL,
    guide_id bigint NOT NULL,
    name character varying(100) NOT NULL,
    icon_id bigint,
    description text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.guide_specialty OWNER TO postgres;

--
-- Name: guide_specialty_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guide_specialty_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guide_specialty_id_seq OWNER TO postgres;

--
-- Name: guide_specialty_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guide_specialty_id_seq OWNED BY public.guide_specialty.id;


--
-- Name: guided_tour; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.guided_tour (
    id bigint NOT NULL,
    guide_id bigint NOT NULL,
    city_id bigint NOT NULL,
    title character varying(200) NOT NULL,
    description text NOT NULL,
    duration_hours numeric(4,2) NOT NULL,
    max_travelers integer DEFAULT 10 NOT NULL,
    base_price numeric(12,2) NOT NULL,
    currency character(3) DEFAULT 'USD'::bpchar NOT NULL,
    meeting_point text,
    includes text,
    excludes text,
    is_private boolean DEFAULT false NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT guided_tour_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text])))
);


ALTER TABLE public.guided_tour OWNER TO postgres;

--
-- Name: guided_tour_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.guided_tour_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.guided_tour_id_seq OWNER TO postgres;

--
-- Name: guided_tour_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.guided_tour_id_seq OWNED BY public.guided_tour.id;


--
-- Name: icon; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.icon (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.icon OWNER TO postgres;

--
-- Name: icon_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.icon_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.icon_id_seq OWNER TO postgres;

--
-- Name: icon_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.icon_id_seq OWNED BY public.icon.id;


--
-- Name: image; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.image (
    id bigint NOT NULL,
    url text NOT NULL,
    alt_text text,
    file_size integer,
    mime_type character varying(50),
    width integer,
    height integer,
    storage_provider character varying(50) DEFAULT 'local'::character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.image OWNER TO postgres;

--
-- Name: image_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.image_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.image_id_seq OWNER TO postgres;

--
-- Name: image_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.image_id_seq OWNED BY public.image.id;


--
-- Name: interest_category; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.interest_category (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    icon_id bigint,
    description text,
    type character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT interest_category_type_check CHECK (((type)::text = ANY (ARRAY[('travel_style'::character varying)::text, ('accommodation'::character varying)::text, ('activity'::character varying)::text, ('cuisine'::character varying)::text, ('general'::character varying)::text])))
);


ALTER TABLE public.interest_category OWNER TO postgres;

--
-- Name: interest_category_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.interest_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.interest_category_id_seq OWNER TO postgres;

--
-- Name: interest_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.interest_category_id_seq OWNED BY public.interest_category.id;


--
-- Name: language; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.language (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.language OWNER TO postgres;

--
-- Name: language_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.language_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.language_id_seq OWNER TO postgres;

--
-- Name: language_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.language_id_seq OWNED BY public.language.id;


--
-- Name: line_station_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.line_station_junction (
    id bigint NOT NULL,
    line_id bigint NOT NULL,
    station_id bigint NOT NULL,
    sequence integer NOT NULL,
    is_terminus boolean DEFAULT false NOT NULL,
    platform text,
    travel_time_to_next integer,
    distance_to_next numeric(8,2),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.line_station_junction OWNER TO postgres;

--
-- Name: line_station_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.line_station_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.line_station_junction_id_seq OWNER TO postgres;

--
-- Name: line_station_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.line_station_junction_id_seq OWNED BY public.line_station_junction.id;


--
-- Name: location; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.location (
    id bigint NOT NULL,
    name character varying(200) NOT NULL,
    address text,
    city_id bigint,
    lat double precision NOT NULL,
    lng double precision NOT NULL,
    type character varying(50) NOT NULL,
    place_id character varying(100),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT location_type_check CHECK (((type)::text = ANY (ARRAY[('hotel'::character varying)::text, ('restaurant'::character varying)::text, ('activity'::character varying)::text, ('airport'::character varying)::text, ('station'::character varying)::text, ('point_of_interest'::character varying)::text])))
);


ALTER TABLE public.location OWNER TO postgres;

--
-- Name: location_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.location_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.location_id_seq OWNER TO postgres;

--
-- Name: location_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.location_id_seq OWNED BY public.location.id;


--
-- Name: meal_type; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.meal_type (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    icon_id bigint,
    typical_time_start time without time zone,
    typical_time_end time without time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.meal_type OWNER TO postgres;

--
-- Name: meal_type_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.meal_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.meal_type_id_seq OWNER TO postgres;

--
-- Name: meal_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.meal_type_id_seq OWNED BY public.meal_type.id;


--
-- Name: notification; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notification (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    title character varying(200) NOT NULL,
    message text NOT NULL,
    type character varying(20) NOT NULL,
    sender_id uuid,
    related_entity_type character varying(50),
    related_entity_id bigint,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_read boolean DEFAULT false NOT NULL,
    action_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT notification_type_check CHECK (((type)::text = ANY (ARRAY[('message'::character varying)::text, ('trip_update'::character varying)::text, ('system'::character varying)::text, ('promotion'::character varying)::text])))
);


ALTER TABLE public.notification OWNER TO postgres;

--
-- Name: notification_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notification_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.notification_id_seq OWNER TO postgres;

--
-- Name: notification_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notification_id_seq OWNED BY public.notification.id;


--
-- Name: profile; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.profile (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(100) NOT NULL,
    phone character varying(20),
    role character varying(20) DEFAULT 'traveler'::character varying NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT profile_role_check CHECK (((role)::text = ANY (ARRAY[('traveler'::character varying)::text, ('guide'::character varying)::text, ('admin'::character varying)::text, ('both'::character varying)::text]))),
    CONSTRAINT profile_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text, ('suspended'::character varying)::text])))
);


ALTER TABLE public.profile OWNER TO postgres;

--
-- Name: restaurant; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.restaurant (
    id bigint NOT NULL,
    name character varying(200) NOT NULL,
    location_id bigint NOT NULL,
    type_id bigint NOT NULL,
    price_range integer NOT NULL,
    rating numeric(2,1),
    description text,
    website text,
    phone character varying(20),
    menu_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT restaurant_price_range_check CHECK (((price_range >= 1) AND (price_range <= 4))),
    CONSTRAINT restaurant_rating_check CHECK (((rating >= (0)::numeric) AND (rating <= (5)::numeric)))
);


ALTER TABLE public.restaurant OWNER TO postgres;

--
-- Name: restaurant_amenity_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.restaurant_amenity_junction (
    id bigint NOT NULL,
    restaurant_id bigint NOT NULL,
    amenity_id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.restaurant_amenity_junction OWNER TO postgres;

--
-- Name: restaurant_amenity_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.restaurant_amenity_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.restaurant_amenity_junction_id_seq OWNER TO postgres;

--
-- Name: restaurant_amenity_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.restaurant_amenity_junction_id_seq OWNED BY public.restaurant_amenity_junction.id;


--
-- Name: restaurant_cuisine_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.restaurant_cuisine_junction (
    id bigint NOT NULL,
    restaurant_id bigint NOT NULL,
    cuisine_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.restaurant_cuisine_junction OWNER TO postgres;

--
-- Name: restaurant_cuisine_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.restaurant_cuisine_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.restaurant_cuisine_junction_id_seq OWNER TO postgres;

--
-- Name: restaurant_cuisine_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.restaurant_cuisine_junction_id_seq OWNED BY public.restaurant_cuisine_junction.id;


--
-- Name: restaurant_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.restaurant_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.restaurant_id_seq OWNER TO postgres;

--
-- Name: restaurant_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.restaurant_id_seq OWNED BY public.restaurant.id;


--
-- Name: restaurant_opening_hours; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.restaurant_opening_hours (
    id bigint NOT NULL,
    restaurant_id bigint NOT NULL,
    day_of_week integer NOT NULL,
    opening_time time without time zone,
    closing_time time without time zone,
    is_closed boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT restaurant_opening_hours_day_of_week_check CHECK (((day_of_week >= 0) AND (day_of_week <= 6)))
);


ALTER TABLE public.restaurant_opening_hours OWNER TO postgres;

--
-- Name: restaurant_opening_hours_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.restaurant_opening_hours_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.restaurant_opening_hours_id_seq OWNER TO postgres;

--
-- Name: restaurant_opening_hours_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.restaurant_opening_hours_id_seq OWNED BY public.restaurant_opening_hours.id;


--
-- Name: restaurant_type; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.restaurant_type (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    description text,
    icon_id bigint,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.restaurant_type OWNER TO postgres;

--
-- Name: restaurant_type_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.restaurant_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.restaurant_type_id_seq OWNER TO postgres;

--
-- Name: restaurant_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.restaurant_type_id_seq OWNED BY public.restaurant_type.id;


--
-- Name: review_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.review_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.review_id_seq OWNER TO postgres;

--
-- Name: review; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.review (
    id bigint DEFAULT nextval('public.review_id_seq'::regclass) NOT NULL,
    entity_type character varying(20) NOT NULL,
    entity_id bigint NOT NULL,
    overall_rating integer,
    formatted_rating character varying(10),
    review_count integer,
    language_tag character varying(10),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT review_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('accommodation'::character varying)::text, ('restaurant'::character varying)::text, ('activity'::character varying)::text]))),
    CONSTRAINT review_overall_rating_check CHECK (((overall_rating >= 0) AND (overall_rating <= 10000)))
);


ALTER TABLE public.review OWNER TO postgres;

--
-- Name: review_aspect_rating_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.review_aspect_rating_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.review_aspect_rating_id_seq OWNER TO postgres;

--
-- Name: review_aspect_rating; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.review_aspect_rating (
    id bigint DEFAULT nextval('public.review_aspect_rating_id_seq'::regclass) NOT NULL,
    review_id bigint NOT NULL,
    aspect_type character varying(50) NOT NULL,
    rating_value integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT review_aspect_rating_value_check CHECK (((rating_value >= 0) AND (rating_value <= 10000)))
);


ALTER TABLE public.review_aspect_rating OWNER TO postgres;

--
-- Name: review_entry_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.review_entry_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.review_entry_id_seq OWNER TO postgres;

--
-- Name: review_entry; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.review_entry (
    id bigint DEFAULT nextval('public.review_entry_id_seq'::regclass) NOT NULL,
    review_id bigint NOT NULL,
    review_text text,
    rating integer,
    author character varying(200),
    travelled_at timestamp with time zone,
    created_at timestamp with time zone,
    advertiser character varying(100),
    CONSTRAINT review_entry_rating_check CHECK (((rating >= 0) AND (rating <= 10000)))
);


ALTER TABLE public.review_entry OWNER TO postgres;

--
-- Name: saved_item; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.saved_item (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    entity_type character varying(20) NOT NULL,
    entity_id bigint NOT NULL,
    notes text,
    saved_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT saved_item_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('city'::character varying)::text, ('hotel'::character varying)::text, ('restaurant'::character varying)::text, ('activity'::character varying)::text, ('attraction'::character varying)::text, ('guided_tour'::character varying)::text])))
);


ALTER TABLE public.saved_item OWNER TO postgres;

--
-- Name: saved_item_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.saved_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.saved_item_id_seq OWNER TO postgres;

--
-- Name: saved_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.saved_item_id_seq OWNED BY public.saved_item.id;


--
-- Name: station; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.station (
    id bigint NOT NULL,
    name text NOT NULL,
    location_id bigint NOT NULL,
    station_type text NOT NULL,
    is_transfer_point boolean DEFAULT false NOT NULL,
    station_code character(3),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.station OWNER TO postgres;

--
-- Name: station_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.station_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.station_id_seq OWNER TO postgres;

--
-- Name: station_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.station_id_seq OWNED BY public.station.id;


--
-- Name: station_transfer; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.station_transfer (
    id bigint NOT NULL,
    from_station_id bigint NOT NULL,
    to_station_id bigint NOT NULL,
    transfer_time_minutes integer NOT NULL,
    transfer_type text DEFAULT 'walking'::text NOT NULL,
    is_free boolean DEFAULT true NOT NULL,
    transfer_instructions text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.station_transfer OWNER TO postgres;

--
-- Name: station_transfer_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.station_transfer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.station_transfer_id_seq OWNER TO postgres;

--
-- Name: station_transfer_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.station_transfer_id_seq OWNED BY public.station_transfer.id;


--
-- Name: tag; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tag (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    category character varying(50),
    icon_id bigint,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.tag OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tag_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tag_id_seq OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tag_id_seq OWNED BY public.tag.id;


--
-- Name: taxi_fare; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.taxi_fare (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    base_fare numeric(10,2) NOT NULL,
    per_km_rate numeric(10,2) NOT NULL,
    per_minute_rate numeric(10,2),
    currency character(3) NOT NULL,
    provider_id bigint,
    taxi_type text DEFAULT 'petit_taxi'::text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.taxi_fare OWNER TO postgres;

--
-- Name: taxi_fare_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.taxi_fare_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.taxi_fare_id_seq OWNER TO postgres;

--
-- Name: taxi_fare_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.taxi_fare_id_seq OWNED BY public.taxi_fare.id;


--
-- Name: tour_highlight; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tour_highlight (
    id bigint NOT NULL,
    tour_id bigint NOT NULL,
    title character varying(200) NOT NULL,
    description text NOT NULL,
    sequence integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.tour_highlight OWNER TO postgres;

--
-- Name: tour_highlight_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tour_highlight_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tour_highlight_id_seq OWNER TO postgres;

--
-- Name: tour_highlight_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tour_highlight_id_seq OWNED BY public.tour_highlight.id;


--
-- Name: tour_itinerary_point; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tour_itinerary_point (
    id bigint NOT NULL,
    tour_id bigint NOT NULL,
    title character varying(200) NOT NULL,
    description text NOT NULL,
    duration_minutes integer NOT NULL,
    sequence integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.tour_itinerary_point OWNER TO postgres;

--
-- Name: tour_itinerary_point_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tour_itinerary_point_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tour_itinerary_point_id_seq OWNER TO postgres;

--
-- Name: tour_itinerary_point_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tour_itinerary_point_id_seq OWNED BY public.tour_itinerary_point.id;


--
-- Name: transport_fare; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transport_fare (
    id bigint NOT NULL,
    mode_id bigint NOT NULL,
    provider_id bigint,
    from_station_id bigint,
    to_station_id bigint,
    city_id bigint,
    line_id bigint,
    service_class text,
    price numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    valid_minutes integer,
    duration_minutes integer,
    distance_km numeric(8,2),
    departure_time timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.transport_fare OWNER TO postgres;

--
-- Name: transport_fare_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transport_fare_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transport_fare_id_seq OWNER TO postgres;

--
-- Name: transport_fare_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transport_fare_id_seq OWNED BY public.transport_fare.id;


--
-- Name: transport_line; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transport_line (
    id bigint NOT NULL,
    provider_id bigint NOT NULL,
    mode_id bigint NOT NULL,
    line_number text NOT NULL,
    name text,
    city_id bigint,
    is_intercity boolean DEFAULT false NOT NULL,
    train_type text,
    color text,
    fare_type text DEFAULT 'fixed'::text NOT NULL,
    fare_amount numeric(12,2),
    currency character(3),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.transport_line OWNER TO postgres;

--
-- Name: transport_line_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transport_line_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transport_line_id_seq OWNER TO postgres;

--
-- Name: transport_line_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transport_line_id_seq OWNED BY public.transport_line.id;


--
-- Name: transport_mode; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transport_mode (
    id bigint NOT NULL,
    name text NOT NULL,
    icon_id bigint,
    description text,
    is_public boolean DEFAULT true NOT NULL,
    is_local boolean DEFAULT true NOT NULL,
    allows_free_transfers boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.transport_mode OWNER TO postgres;

--
-- Name: transport_mode_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transport_mode_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transport_mode_id_seq OWNER TO postgres;

--
-- Name: transport_mode_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transport_mode_id_seq OWNED BY public.transport_mode.id;


--
-- Name: transport_provider; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transport_provider (
    id bigint NOT NULL,
    name text NOT NULL,
    website text,
    contact_phone text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.transport_provider OWNER TO postgres;

--
-- Name: transport_provider_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transport_provider_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transport_provider_id_seq OWNER TO postgres;

--
-- Name: transport_provider_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transport_provider_id_seq OWNED BY public.transport_provider.id;


--
-- Name: trip; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    title character varying(200) NOT NULL,
    description text,
    start_date date NOT NULL,
    end_date date NOT NULL,
    budget_total numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    origin_airport_id bigint,
    status character varying(20) DEFAULT 'draft'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_check CHECK ((end_date >= start_date)),
    CONSTRAINT trip_status_check CHECK (((status)::text = ANY (ARRAY[('draft'::character varying)::text, ('confirmed'::character varying)::text, ('in_progress'::character varying)::text, ('completed'::character varying)::text, ('cancelled'::character varying)::text])))
);


ALTER TABLE public.trip OWNER TO postgres;

--
-- Name: trip_accommodation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_accommodation (
    id bigint NOT NULL,
    trip_id bigint NOT NULL,
    trip_day_id bigint NOT NULL,
    accommodation_id bigint NOT NULL,
    room_id bigint NOT NULL,
    check_in_date date NOT NULL,
    check_out_date date NOT NULL,
    nights integer NOT NULL,
    guests integer DEFAULT 1 NOT NULL,
    price_per_night numeric(12,2) NOT NULL,
    total_price numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    booking_reference character varying(100),
    booking_status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    special_requests text,
    notes text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_accommodation_booking_status_check CHECK (((booking_status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('pending'::character varying)::text, ('cancelled'::character varying)::text])))
);


ALTER TABLE public.trip_accommodation OWNER TO postgres;

--
-- Name: trip_accommodation_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_accommodation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_accommodation_id_seq OWNER TO postgres;

--
-- Name: trip_accommodation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_accommodation_id_seq OWNED BY public.trip_accommodation.id;


--
-- Name: trip_activity; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_activity (
    id bigint NOT NULL,
    trip_day_id bigint NOT NULL,
    activity_id bigint NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    description text,
    total_price numeric(12,2),
    currency character(3),
    booking_status character varying(20) DEFAULT 'recommended'::character varying NOT NULL,
    booking_reference character varying(100),
    meeting_point text,
    notes text,
    sequence integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_activity_booking_status_check CHECK (((booking_status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('pending'::character varying)::text, ('recommended'::character varying)::text])))
);


ALTER TABLE public.trip_activity OWNER TO postgres;

--
-- Name: trip_activity_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_activity_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_activity_id_seq OWNER TO postgres;

--
-- Name: trip_activity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_activity_id_seq OWNED BY public.trip_activity.id;


--
-- Name: trip_change_log; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_change_log (
    id bigint NOT NULL,
    trip_id bigint NOT NULL,
    user_id uuid NOT NULL,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    change_type character varying(30) NOT NULL,
    entity_type character varying(20) NOT NULL,
    entity_id bigint NOT NULL,
    field_name character varying(50) NOT NULL,
    old_value text,
    new_value text,
    change_reason text,
    system_note text,
    user_note text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_change_log_change_type_check CHECK (((change_type)::text = ANY (ARRAY[('created'::character varying)::text, ('updated'::character varying)::text, ('cancelled'::character varying)::text, ('rescheduled'::character varying)::text, ('budget_changed'::character varying)::text, ('item_added'::character varying)::text, ('item_removed'::character varying)::text, ('item_modified'::character varying)::text]))),
    CONSTRAINT trip_change_log_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('trip'::character varying)::text, ('accommodation'::character varying)::text, ('activity'::character varying)::text, ('meal'::character varying)::text, ('transport'::character varying)::text])))
);


ALTER TABLE public.trip_change_log OWNER TO postgres;

--
-- Name: trip_change_log_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_change_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_change_log_id_seq OWNER TO postgres;

--
-- Name: trip_change_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_change_log_id_seq OWNED BY public.trip_change_log.id;


--
-- Name: trip_day; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_day (
    id bigint NOT NULL,
    trip_id bigint NOT NULL,
    day_number integer NOT NULL,
    date date NOT NULL,
    city_id bigint NOT NULL,
    daily_budget numeric(12,2),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.trip_day OWNER TO postgres;

--
-- Name: trip_day_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_day_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_day_id_seq OWNER TO postgres;

--
-- Name: trip_day_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_day_id_seq OWNED BY public.trip_day.id;


--
-- Name: trip_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_id_seq OWNER TO postgres;

--
-- Name: trip_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_id_seq OWNED BY public.trip.id;


--
-- Name: trip_interest_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_interest_junction (
    id bigint NOT NULL,
    trip_id bigint NOT NULL,
    interest_id bigint NOT NULL,
    weight integer DEFAULT 5 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_interest_junction_weight_check CHECK (((weight >= 1) AND (weight <= 10)))
);


ALTER TABLE public.trip_interest_junction OWNER TO postgres;

--
-- Name: trip_interest_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_interest_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_interest_junction_id_seq OWNER TO postgres;

--
-- Name: trip_interest_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_interest_junction_id_seq OWNED BY public.trip_interest_junction.id;


--
-- Name: trip_meal; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_meal (
    id bigint NOT NULL,
    trip_day_id bigint NOT NULL,
    meal_type_id bigint NOT NULL,
    restaurant_id bigint NOT NULL,
    "time" time without time zone NOT NULL,
    duration_minutes integer DEFAULT 60,
    description text,
    price_per_person numeric(10,2),
    total_price numeric(12,2),
    currency character(3),
    reservation_status character varying(20) DEFAULT 'none'::character varying NOT NULL,
    reservation_time time without time zone,
    reservation_reference character varying(100),
    notes text,
    sequence integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_meal_reservation_status_check CHECK (((reservation_status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('recommended'::character varying)::text, ('none'::character varying)::text])))
);


ALTER TABLE public.trip_meal OWNER TO postgres;

--
-- Name: trip_meal_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_meal_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_meal_id_seq OWNER TO postgres;

--
-- Name: trip_meal_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_meal_id_seq OWNED BY public.trip_meal.id;


--
-- Name: trip_transport; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.trip_transport (
    id bigint NOT NULL,
    trip_day_id bigint NOT NULL,
    mode_id bigint NOT NULL,
    from_location_id bigint NOT NULL,
    to_location_id bigint NOT NULL,
    departure_time timestamp with time zone NOT NULL,
    arrival_time timestamp with time zone NOT NULL,
    provider_id bigint,
    line_id bigint,
    fare_id bigint,
    price numeric(12,2) NOT NULL,
    currency character(3) NOT NULL,
    booking_reference character varying(100),
    status character varying(20) DEFAULT 'recommended'::character varying NOT NULL,
    sequence integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT trip_transport_status_check CHECK (((status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('pending'::character varying)::text, ('recommended'::character varying)::text])))
);


ALTER TABLE public.trip_transport OWNER TO postgres;

--
-- Name: trip_transport_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.trip_transport_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.trip_transport_id_seq OWNER TO postgres;

--
-- Name: trip_transport_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.trip_transport_id_seq OWNED BY public.trip_transport.id;


--
-- Name: user_interest_junction; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_interest_junction (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    interest_id bigint NOT NULL,
    weight integer DEFAULT 5 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_interest_junction_weight_check CHECK (((weight >= 1) AND (weight <= 10)))
);


ALTER TABLE public.user_interest_junction OWNER TO postgres;

--
-- Name: user_interest_junction_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_interest_junction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_interest_junction_id_seq OWNER TO postgres;

--
-- Name: user_interest_junction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_interest_junction_id_seq OWNED BY public.user_interest_junction.id;


--
-- Name: user_system_preference; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_system_preference (
    id bigint NOT NULL,
    user_id uuid NOT NULL,
    default_currency character(3) DEFAULT 'USD'::bpchar NOT NULL,
    default_language character varying(50) DEFAULT 'English'::character varying NOT NULL,
    timezone character varying(50) DEFAULT 'UTC'::character varying NOT NULL,
    date_format character varying(20) DEFAULT 'YYYY-MM-DD'::character varying NOT NULL,
    email_notifications boolean DEFAULT true NOT NULL,
    sms_notifications boolean DEFAULT false NOT NULL,
    push_notifications boolean DEFAULT true NOT NULL,
    trip_updates boolean DEFAULT true NOT NULL,
    marketing boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.user_system_preference OWNER TO postgres;

--
-- Name: user_system_preference_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_system_preference_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_system_preference_id_seq OWNER TO postgres;

--
-- Name: user_system_preference_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_system_preference_id_seq OWNED BY public.user_system_preference.id;


--
-- Name: accommodation id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation ALTER COLUMN id SET DEFAULT nextval('public.accommodation_id_seq'::regclass);


--
-- Name: accommodation_amenity_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction ALTER COLUMN id SET DEFAULT nextval('public.accommodation_amenity_junction_id_seq'::regclass);


--
-- Name: accommodation_rate id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_rate ALTER COLUMN id SET DEFAULT nextval('public.accommodation_rate_id_seq'::regclass);


--
-- Name: accommodation_room id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_room ALTER COLUMN id SET DEFAULT nextval('public.accommodation_room_id_seq'::regclass);


--
-- Name: accommodation_type id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_type ALTER COLUMN id SET DEFAULT nextval('public.accommodation_type_id_seq'::regclass);


--
-- Name: activity id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity ALTER COLUMN id SET DEFAULT nextval('public.activity_id_seq'::regclass);


--
-- Name: activity_category id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_category ALTER COLUMN id SET DEFAULT nextval('public.activity_category_id_seq'::regclass);


--
-- Name: activity_opening_hours id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_opening_hours ALTER COLUMN id SET DEFAULT nextval('public.activity_opening_hours_id_seq'::regclass);


--
-- Name: activity_tag_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction ALTER COLUMN id SET DEFAULT nextval('public.activity_tag_junction_id_seq'::regclass);


--
-- Name: amenity id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.amenity ALTER COLUMN id SET DEFAULT nextval('public.amenity_id_seq'::regclass);


--
-- Name: budget_allocation id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation ALTER COLUMN id SET DEFAULT nextval('public.budget_allocation_id_seq'::regclass);


--
-- Name: budget_category id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_category ALTER COLUMN id SET DEFAULT nextval('public.budget_category_id_seq'::regclass);


--
-- Name: car_rental id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental ALTER COLUMN id SET DEFAULT nextval('public.car_rental_id_seq'::regclass);


--
-- Name: city id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.city ALTER COLUMN id SET DEFAULT nextval('public.city_id_seq'::regclass);


--
-- Name: country id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country ALTER COLUMN id SET DEFAULT nextval('public.country_id_seq'::regclass);


--
-- Name: country_custom id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_custom ALTER COLUMN id SET DEFAULT nextval('public.country_custom_id_seq'::regclass);


--
-- Name: country_emergency_contact id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_emergency_contact ALTER COLUMN id SET DEFAULT nextval('public.country_emergency_contact_id_seq'::regclass);


--
-- Name: cuisine_type id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cuisine_type ALTER COLUMN id SET DEFAULT nextval('public.cuisine_type_id_seq'::regclass);


--
-- Name: daily_itinerary id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary ALTER COLUMN id SET DEFAULT nextval('public.daily_itinerary_id_seq'::regclass);


--
-- Name: daily_itinerary_item id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary_item ALTER COLUMN id SET DEFAULT nextval('public.daily_itinerary_item_id_seq'::regclass);


--
-- Name: entity_image id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_image ALTER COLUMN id SET DEFAULT nextval('public.entity_image_id_seq'::regclass);


--
-- Name: expense id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense ALTER COLUMN id SET DEFAULT nextval('public.expense_id_seq'::regclass);


--
-- Name: guide_availability id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_availability ALTER COLUMN id SET DEFAULT nextval('public.guide_availability_id_seq'::regclass);


--
-- Name: guide_language_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction ALTER COLUMN id SET DEFAULT nextval('public.guide_language_junction_id_seq'::regclass);


--
-- Name: guide_location_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction ALTER COLUMN id SET DEFAULT nextval('public.guide_location_junction_id_seq'::regclass);


--
-- Name: guide_profile id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_profile ALTER COLUMN id SET DEFAULT nextval('public.guide_profile_id_seq'::regclass);


--
-- Name: guide_request id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request ALTER COLUMN id SET DEFAULT nextval('public.guide_request_id_seq'::regclass);


--
-- Name: guide_specialty id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_specialty ALTER COLUMN id SET DEFAULT nextval('public.guide_specialty_id_seq'::regclass);


--
-- Name: guided_tour id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guided_tour ALTER COLUMN id SET DEFAULT nextval('public.guided_tour_id_seq'::regclass);


--
-- Name: icon id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.icon ALTER COLUMN id SET DEFAULT nextval('public.icon_id_seq'::regclass);


--
-- Name: image id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.image ALTER COLUMN id SET DEFAULT nextval('public.image_id_seq'::regclass);


--
-- Name: interest_category id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.interest_category ALTER COLUMN id SET DEFAULT nextval('public.interest_category_id_seq'::regclass);


--
-- Name: language id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.language ALTER COLUMN id SET DEFAULT nextval('public.language_id_seq'::regclass);


--
-- Name: line_station_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction ALTER COLUMN id SET DEFAULT nextval('public.line_station_junction_id_seq'::regclass);


--
-- Name: location id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.location ALTER COLUMN id SET DEFAULT nextval('public.location_id_seq'::regclass);


--
-- Name: meal_type id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_type ALTER COLUMN id SET DEFAULT nextval('public.meal_type_id_seq'::regclass);


--
-- Name: notification id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification ALTER COLUMN id SET DEFAULT nextval('public.notification_id_seq'::regclass);


--
-- Name: restaurant id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant ALTER COLUMN id SET DEFAULT nextval('public.restaurant_id_seq'::regclass);


--
-- Name: restaurant_amenity_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction ALTER COLUMN id SET DEFAULT nextval('public.restaurant_amenity_junction_id_seq'::regclass);


--
-- Name: restaurant_cuisine_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction ALTER COLUMN id SET DEFAULT nextval('public.restaurant_cuisine_junction_id_seq'::regclass);


--
-- Name: restaurant_opening_hours id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_opening_hours ALTER COLUMN id SET DEFAULT nextval('public.restaurant_opening_hours_id_seq'::regclass);


--
-- Name: restaurant_type id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_type ALTER COLUMN id SET DEFAULT nextval('public.restaurant_type_id_seq'::regclass);


--
-- Name: saved_item id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.saved_item ALTER COLUMN id SET DEFAULT nextval('public.saved_item_id_seq'::regclass);


--
-- Name: station id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station ALTER COLUMN id SET DEFAULT nextval('public.station_id_seq'::regclass);


--
-- Name: station_transfer id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer ALTER COLUMN id SET DEFAULT nextval('public.station_transfer_id_seq'::regclass);


--
-- Name: tag id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag ALTER COLUMN id SET DEFAULT nextval('public.tag_id_seq'::regclass);


--
-- Name: taxi_fare id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.taxi_fare ALTER COLUMN id SET DEFAULT nextval('public.taxi_fare_id_seq'::regclass);


--
-- Name: tour_highlight id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_highlight ALTER COLUMN id SET DEFAULT nextval('public.tour_highlight_id_seq'::regclass);


--
-- Name: tour_itinerary_point id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_itinerary_point ALTER COLUMN id SET DEFAULT nextval('public.tour_itinerary_point_id_seq'::regclass);


--
-- Name: transport_fare id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare ALTER COLUMN id SET DEFAULT nextval('public.transport_fare_id_seq'::regclass);


--
-- Name: transport_line id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line ALTER COLUMN id SET DEFAULT nextval('public.transport_line_id_seq'::regclass);


--
-- Name: transport_mode id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_mode ALTER COLUMN id SET DEFAULT nextval('public.transport_mode_id_seq'::regclass);


--
-- Name: transport_provider id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_provider ALTER COLUMN id SET DEFAULT nextval('public.transport_provider_id_seq'::regclass);


--
-- Name: trip id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip ALTER COLUMN id SET DEFAULT nextval('public.trip_id_seq'::regclass);


--
-- Name: trip_accommodation id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation ALTER COLUMN id SET DEFAULT nextval('public.trip_accommodation_id_seq'::regclass);


--
-- Name: trip_activity id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_activity ALTER COLUMN id SET DEFAULT nextval('public.trip_activity_id_seq'::regclass);


--
-- Name: trip_change_log id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_change_log ALTER COLUMN id SET DEFAULT nextval('public.trip_change_log_id_seq'::regclass);


--
-- Name: trip_day id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day ALTER COLUMN id SET DEFAULT nextval('public.trip_day_id_seq'::regclass);


--
-- Name: trip_interest_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction ALTER COLUMN id SET DEFAULT nextval('public.trip_interest_junction_id_seq'::regclass);


--
-- Name: trip_meal id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal ALTER COLUMN id SET DEFAULT nextval('public.trip_meal_id_seq'::regclass);


--
-- Name: trip_transport id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport ALTER COLUMN id SET DEFAULT nextval('public.trip_transport_id_seq'::regclass);


--
-- Name: user_interest_junction id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interest_junction ALTER COLUMN id SET DEFAULT nextval('public.user_interest_junction_id_seq'::regclass);


--
-- Name: user_system_preference id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_system_preference ALTER COLUMN id SET DEFAULT nextval('public.user_system_preference_id_seq'::regclass);


--
-- Name: accommodation_amenity_junction accommodation_amenity_junction_accommodation_id_amenity_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction
    ADD CONSTRAINT accommodation_amenity_junction_accommodation_id_amenity_id_key UNIQUE (accommodation_id, amenity_id);


--
-- Name: accommodation_amenity_junction accommodation_amenity_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction
    ADD CONSTRAINT accommodation_amenity_junction_pkey PRIMARY KEY (id);


--
-- Name: accommodation accommodation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation
    ADD CONSTRAINT accommodation_pkey PRIMARY KEY (id);


--
-- Name: accommodation_price_forecast accommodation_price_forecast_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_price_forecast
    ADD CONSTRAINT accommodation_price_forecast_pkey PRIMARY KEY (id);


--
-- Name: accommodation_price_forecast accommodation_price_forecast_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_price_forecast
    ADD CONSTRAINT accommodation_price_forecast_unique UNIQUE (accommodation_id, forecast_month);


--
-- Name: accommodation_rate accommodation_rate_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_rate
    ADD CONSTRAINT accommodation_rate_pkey PRIMARY KEY (id);


--
-- Name: accommodation_room accommodation_room_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_room
    ADD CONSTRAINT accommodation_room_pkey PRIMARY KEY (id);


--
-- Name: accommodation_type accommodation_type_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_type
    ADD CONSTRAINT accommodation_type_name_key UNIQUE (name);


--
-- Name: accommodation_type accommodation_type_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_type
    ADD CONSTRAINT accommodation_type_pkey PRIMARY KEY (id);


--
-- Name: activity_category activity_category_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_category
    ADD CONSTRAINT activity_category_name_key UNIQUE (name);


--
-- Name: activity_category activity_category_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_category
    ADD CONSTRAINT activity_category_pkey PRIMARY KEY (id);


--
-- Name: activity_opening_hours activity_opening_hours_activity_id_day_of_week_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_opening_hours
    ADD CONSTRAINT activity_opening_hours_activity_id_day_of_week_key UNIQUE (activity_id, day_of_week);


--
-- Name: activity_opening_hours activity_opening_hours_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_opening_hours
    ADD CONSTRAINT activity_opening_hours_pkey PRIMARY KEY (id);


--
-- Name: activity activity_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity
    ADD CONSTRAINT activity_pkey PRIMARY KEY (id);


--
-- Name: activity_tag_junction activity_tag_junction_activity_id_tag_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction
    ADD CONSTRAINT activity_tag_junction_activity_id_tag_id_key UNIQUE (activity_id, tag_id);


--
-- Name: activity_tag_junction activity_tag_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction
    ADD CONSTRAINT activity_tag_junction_pkey PRIMARY KEY (id);


--
-- Name: amenity amenity_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.amenity
    ADD CONSTRAINT amenity_name_key UNIQUE (name);


--
-- Name: amenity amenity_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.amenity
    ADD CONSTRAINT amenity_pkey PRIMARY KEY (id);


--
-- Name: budget_allocation budget_allocation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation
    ADD CONSTRAINT budget_allocation_pkey PRIMARY KEY (id);


--
-- Name: budget_allocation budget_allocation_trip_id_category_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation
    ADD CONSTRAINT budget_allocation_trip_id_category_id_key UNIQUE (trip_id, category_id);


--
-- Name: budget_category budget_category_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_category
    ADD CONSTRAINT budget_category_name_key UNIQUE (name);


--
-- Name: budget_category budget_category_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_category
    ADD CONSTRAINT budget_category_pkey PRIMARY KEY (id);


--
-- Name: car_rental car_rental_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT car_rental_pkey PRIMARY KEY (id);


--
-- Name: city city_name_country_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.city
    ADD CONSTRAINT city_name_country_id_key UNIQUE (name, country_id);


--
-- Name: city city_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.city
    ADD CONSTRAINT city_pkey PRIMARY KEY (id);


--
-- Name: country country_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country
    ADD CONSTRAINT country_code_key UNIQUE (code);


--
-- Name: country_custom country_custom_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_custom
    ADD CONSTRAINT country_custom_pkey PRIMARY KEY (id);


--
-- Name: country_emergency_contact country_emergency_contact_country_id_service_type_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_emergency_contact
    ADD CONSTRAINT country_emergency_contact_country_id_service_type_key UNIQUE (country_id, service_type);


--
-- Name: country_emergency_contact country_emergency_contact_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_emergency_contact
    ADD CONSTRAINT country_emergency_contact_pkey PRIMARY KEY (id);


--
-- Name: country country_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country
    ADD CONSTRAINT country_name_key UNIQUE (name);


--
-- Name: country country_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country
    ADD CONSTRAINT country_pkey PRIMARY KEY (id);


--
-- Name: cuisine_type cuisine_type_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cuisine_type
    ADD CONSTRAINT cuisine_type_name_key UNIQUE (name);


--
-- Name: cuisine_type cuisine_type_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cuisine_type
    ADD CONSTRAINT cuisine_type_pkey PRIMARY KEY (id);


--
-- Name: daily_itinerary_item daily_itinerary_item_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary_item
    ADD CONSTRAINT daily_itinerary_item_pkey PRIMARY KEY (id);


--
-- Name: daily_itinerary daily_itinerary_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary
    ADD CONSTRAINT daily_itinerary_pkey PRIMARY KEY (id);


--
-- Name: daily_itinerary daily_itinerary_trip_day_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary
    ADD CONSTRAINT daily_itinerary_trip_day_id_key UNIQUE (trip_day_id);


--
-- Name: entity_image entity_image_entity_type_entity_id_image_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_image
    ADD CONSTRAINT entity_image_entity_type_entity_id_image_id_key UNIQUE (entity_type, entity_id, image_id);


--
-- Name: entity_image entity_image_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_image
    ADD CONSTRAINT entity_image_pkey PRIMARY KEY (id);


--
-- Name: expense_allocation expense_allocation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense_allocation
    ADD CONSTRAINT expense_allocation_pkey PRIMARY KEY (expense_id, entity_type, entity_id);


--
-- Name: expense expense_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT expense_pkey PRIMARY KEY (id);


--
-- Name: guide_availability guide_availability_guide_id_date_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_availability
    ADD CONSTRAINT guide_availability_guide_id_date_key UNIQUE (guide_id, date);


--
-- Name: guide_availability guide_availability_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_availability
    ADD CONSTRAINT guide_availability_pkey PRIMARY KEY (id);


--
-- Name: guide_language_junction guide_language_junction_guide_id_language_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction
    ADD CONSTRAINT guide_language_junction_guide_id_language_id_key UNIQUE (guide_id, language_id);


--
-- Name: guide_language_junction guide_language_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction
    ADD CONSTRAINT guide_language_junction_pkey PRIMARY KEY (id);


--
-- Name: guide_location_junction guide_location_junction_guide_id_city_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction
    ADD CONSTRAINT guide_location_junction_guide_id_city_id_key UNIQUE (guide_id, city_id);


--
-- Name: guide_location_junction guide_location_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction
    ADD CONSTRAINT guide_location_junction_pkey PRIMARY KEY (id);


--
-- Name: guide_profile guide_profile_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_profile
    ADD CONSTRAINT guide_profile_pkey PRIMARY KEY (id);


--
-- Name: guide_profile guide_profile_user_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_profile
    ADD CONSTRAINT guide_profile_user_id_key UNIQUE (user_id);


--
-- Name: guide_request guide_request_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT guide_request_pkey PRIMARY KEY (id);


--
-- Name: guide_specialty guide_specialty_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_specialty
    ADD CONSTRAINT guide_specialty_pkey PRIMARY KEY (id);


--
-- Name: guided_tour guided_tour_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guided_tour
    ADD CONSTRAINT guided_tour_pkey PRIMARY KEY (id);


--
-- Name: icon icon_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.icon
    ADD CONSTRAINT icon_name_key UNIQUE (name);


--
-- Name: icon icon_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.icon
    ADD CONSTRAINT icon_pkey PRIMARY KEY (id);


--
-- Name: image image_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.image
    ADD CONSTRAINT image_pkey PRIMARY KEY (id);


--
-- Name: interest_category interest_category_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.interest_category
    ADD CONSTRAINT interest_category_name_key UNIQUE (name);


--
-- Name: interest_category interest_category_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.interest_category
    ADD CONSTRAINT interest_category_pkey PRIMARY KEY (id);


--
-- Name: language language_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.language
    ADD CONSTRAINT language_name_key UNIQUE (name);


--
-- Name: language language_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.language
    ADD CONSTRAINT language_pkey PRIMARY KEY (id);


--
-- Name: line_station_junction line_station_junction_line_id_station_id_sequence_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction
    ADD CONSTRAINT line_station_junction_line_id_station_id_sequence_key UNIQUE (line_id, station_id, sequence);


--
-- Name: line_station_junction line_station_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction
    ADD CONSTRAINT line_station_junction_pkey PRIMARY KEY (id);


--
-- Name: location location_lat_lng_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.location
    ADD CONSTRAINT location_lat_lng_key UNIQUE (lat, lng);


--
-- Name: location location_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.location
    ADD CONSTRAINT location_pkey PRIMARY KEY (id);


--
-- Name: location location_place_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.location
    ADD CONSTRAINT location_place_id_key UNIQUE (place_id);


--
-- Name: meal_type meal_type_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_type
    ADD CONSTRAINT meal_type_name_key UNIQUE (name);


--
-- Name: meal_type meal_type_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_type
    ADD CONSTRAINT meal_type_pkey PRIMARY KEY (id);


--
-- Name: notification notification_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT notification_pkey PRIMARY KEY (id);


--
-- Name: profile profile_phone_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.profile
    ADD CONSTRAINT profile_phone_unique UNIQUE (phone);


--
-- Name: profile profile_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.profile
    ADD CONSTRAINT profile_pkey PRIMARY KEY (id);


--
-- Name: restaurant_amenity_junction restaurant_amenity_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction
    ADD CONSTRAINT restaurant_amenity_junction_pkey PRIMARY KEY (id);


--
-- Name: restaurant_amenity_junction restaurant_amenity_junction_restaurant_id_amenity_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction
    ADD CONSTRAINT restaurant_amenity_junction_restaurant_id_amenity_id_key UNIQUE (restaurant_id, amenity_id);


--
-- Name: restaurant_cuisine_junction restaurant_cuisine_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction
    ADD CONSTRAINT restaurant_cuisine_junction_pkey PRIMARY KEY (id);


--
-- Name: restaurant_cuisine_junction restaurant_cuisine_junction_restaurant_id_cuisine_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction
    ADD CONSTRAINT restaurant_cuisine_junction_restaurant_id_cuisine_id_key UNIQUE (restaurant_id, cuisine_id);


--
-- Name: restaurant_opening_hours restaurant_opening_hours_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_opening_hours
    ADD CONSTRAINT restaurant_opening_hours_pkey PRIMARY KEY (id);


--
-- Name: restaurant_opening_hours restaurant_opening_hours_restaurant_id_day_of_week_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_opening_hours
    ADD CONSTRAINT restaurant_opening_hours_restaurant_id_day_of_week_key UNIQUE (restaurant_id, day_of_week);


--
-- Name: restaurant restaurant_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant
    ADD CONSTRAINT restaurant_pkey PRIMARY KEY (id);


--
-- Name: restaurant_type restaurant_type_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_type
    ADD CONSTRAINT restaurant_type_name_key UNIQUE (name);


--
-- Name: restaurant_type restaurant_type_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_type
    ADD CONSTRAINT restaurant_type_pkey PRIMARY KEY (id);


--
-- Name: review_aspect_rating review_aspect_rating_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.review_aspect_rating
    ADD CONSTRAINT review_aspect_rating_pkey PRIMARY KEY (id);


--
-- Name: review review_entity_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.review
    ADD CONSTRAINT review_entity_unique UNIQUE (entity_type, entity_id);


--
-- Name: review_entry review_entry_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.review_entry
    ADD CONSTRAINT review_entry_pkey PRIMARY KEY (id);


--
-- Name: review review_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.review
    ADD CONSTRAINT review_pkey PRIMARY KEY (id);


--
-- Name: saved_item saved_item_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.saved_item
    ADD CONSTRAINT saved_item_pkey PRIMARY KEY (id);


--
-- Name: saved_item saved_item_user_id_entity_type_entity_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.saved_item
    ADD CONSTRAINT saved_item_user_id_entity_type_entity_id_key UNIQUE (user_id, entity_type, entity_id);


--
-- Name: station station_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station
    ADD CONSTRAINT station_pkey PRIMARY KEY (id);


--
-- Name: station station_station_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station
    ADD CONSTRAINT station_station_code_key UNIQUE (station_code);


--
-- Name: station_transfer station_transfer_from_station_id_to_station_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer
    ADD CONSTRAINT station_transfer_from_station_id_to_station_id_key UNIQUE (from_station_id, to_station_id);


--
-- Name: station_transfer station_transfer_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer
    ADD CONSTRAINT station_transfer_pkey PRIMARY KEY (id);


--
-- Name: tag tag_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT tag_name_key UNIQUE (name);


--
-- Name: tag tag_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT tag_pkey PRIMARY KEY (id);


--
-- Name: taxi_fare taxi_fare_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.taxi_fare
    ADD CONSTRAINT taxi_fare_pkey PRIMARY KEY (id);


--
-- Name: tour_highlight tour_highlight_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_highlight
    ADD CONSTRAINT tour_highlight_pkey PRIMARY KEY (id);


--
-- Name: tour_itinerary_point tour_itinerary_point_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_itinerary_point
    ADD CONSTRAINT tour_itinerary_point_pkey PRIMARY KEY (id);


--
-- Name: transport_fare transport_fare_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_pkey PRIMARY KEY (id);


--
-- Name: transport_line transport_line_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT transport_line_pkey PRIMARY KEY (id);


--
-- Name: transport_line transport_line_provider_id_line_number_city_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT transport_line_provider_id_line_number_city_id_key UNIQUE (provider_id, line_number, city_id);


--
-- Name: transport_mode transport_mode_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_mode
    ADD CONSTRAINT transport_mode_name_key UNIQUE (name);


--
-- Name: transport_mode transport_mode_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_mode
    ADD CONSTRAINT transport_mode_pkey PRIMARY KEY (id);


--
-- Name: transport_provider transport_provider_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_provider
    ADD CONSTRAINT transport_provider_name_key UNIQUE (name);


--
-- Name: transport_provider transport_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_provider
    ADD CONSTRAINT transport_provider_pkey PRIMARY KEY (id);


--
-- Name: trip_accommodation trip_accommodation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT trip_accommodation_pkey PRIMARY KEY (id);


--
-- Name: trip_accommodation trip_accommodation_trip_id_trip_day_id_accommodation_id_roo_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT trip_accommodation_trip_id_trip_day_id_accommodation_id_roo_key UNIQUE (trip_id, trip_day_id, accommodation_id, room_id);


--
-- Name: trip_activity trip_activity_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_activity
    ADD CONSTRAINT trip_activity_pkey PRIMARY KEY (id);


--
-- Name: trip_change_log trip_change_log_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_change_log
    ADD CONSTRAINT trip_change_log_pkey PRIMARY KEY (id);


--
-- Name: trip_day trip_day_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT trip_day_pkey PRIMARY KEY (id);


--
-- Name: trip_day trip_day_trip_id_date_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT trip_day_trip_id_date_key UNIQUE (trip_id, date);


--
-- Name: trip_day trip_day_trip_id_day_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT trip_day_trip_id_day_number_key UNIQUE (trip_id, day_number);


--
-- Name: trip_interest_junction trip_interest_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction
    ADD CONSTRAINT trip_interest_junction_pkey PRIMARY KEY (id);


--
-- Name: trip_interest_junction trip_interest_junction_trip_id_interest_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction
    ADD CONSTRAINT trip_interest_junction_trip_id_interest_id_key UNIQUE (trip_id, interest_id);


--
-- Name: trip_meal trip_meal_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT trip_meal_pkey PRIMARY KEY (id);


--
-- Name: trip trip_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip
    ADD CONSTRAINT trip_pkey PRIMARY KEY (id);


--
-- Name: trip_transport trip_transport_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_pkey PRIMARY KEY (id);


--
-- Name: user_interest_junction user_interest_junction_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interest_junction
    ADD CONSTRAINT user_interest_junction_pkey PRIMARY KEY (id);


--
-- Name: user_interest_junction user_interest_junction_user_id_interest_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interest_junction
    ADD CONSTRAINT user_interest_junction_user_id_interest_id_key UNIQUE (user_id, interest_id);


--
-- Name: user_system_preference user_system_preference_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_system_preference
    ADD CONSTRAINT user_system_preference_pkey PRIMARY KEY (id);


--
-- Name: user_system_preference user_system_preference_user_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_system_preference
    ADD CONSTRAINT user_system_preference_user_id_key UNIQUE (user_id);


--
-- Name: idx_accommodation_amenity_junction_free; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accommodation_amenity_junction_free ON public.accommodation_amenity_junction USING btree (accommodation_id, is_free) WHERE (is_free = true);


--
-- Name: idx_accommodation_amenity_junction_top; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accommodation_amenity_junction_top ON public.accommodation_amenity_junction USING btree (accommodation_id, is_top_amenity) WHERE (is_top_amenity = true);


--
-- Name: idx_accommodation_external_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accommodation_external_id ON public.accommodation USING btree (external_id) WHERE (external_id IS NOT NULL);


--
-- Name: idx_accommodation_price_forecast_accommodation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accommodation_price_forecast_accommodation_id ON public.accommodation_price_forecast USING btree (accommodation_id);


--
-- Name: idx_accommodation_price_forecast_month; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accommodation_price_forecast_month ON public.accommodation_price_forecast USING btree (forecast_month);


--
-- Name: idx_entity_image_entity; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_entity_image_entity ON public.entity_image USING btree (entity_type, entity_id);


--
-- Name: idx_entity_image_featured; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_entity_image_featured ON public.entity_image USING btree (entity_type, entity_id, is_featured) WHERE (is_featured = true);


--
-- Name: idx_entity_image_one_featured; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_entity_image_one_featured ON public.entity_image USING btree (entity_type, entity_id) WHERE (is_featured = true);


--
-- Name: idx_entity_image_sequence; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_entity_image_sequence ON public.entity_image USING btree (entity_type, entity_id, sequence);


--
-- Name: idx_image_url; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_image_url ON public.image USING btree (url);


--
-- Name: idx_review_aspect_rating_review_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_review_aspect_rating_review_id ON public.review_aspect_rating USING btree (review_id);


--
-- Name: idx_review_entity; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_review_entity ON public.review USING btree (entity_type, entity_id);


--
-- Name: idx_review_entry_review_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_review_entry_review_id ON public.review_entry USING btree (review_id);


--
-- Name: accommodation_amenity_junction accommodation_amenity_junction_accommodation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction
    ADD CONSTRAINT accommodation_amenity_junction_accommodation_id_fkey FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation_amenity_junction accommodation_amenity_junction_amenity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction
    ADD CONSTRAINT accommodation_amenity_junction_amenity_id_fkey FOREIGN KEY (amenity_id) REFERENCES public.amenity(id);


--
-- Name: accommodation accommodation_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation
    ADD CONSTRAINT accommodation_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: accommodation_rate accommodation_rate_accommodation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_rate
    ADD CONSTRAINT accommodation_rate_accommodation_id_fkey FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation_rate accommodation_rate_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_rate
    ADD CONSTRAINT accommodation_rate_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.accommodation_room(id) ON DELETE CASCADE;


--
-- Name: accommodation_room accommodation_room_accommodation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_room
    ADD CONSTRAINT accommodation_room_accommodation_id_fkey FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation_type accommodation_type_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_type
    ADD CONSTRAINT accommodation_type_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: accommodation accommodation_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation
    ADD CONSTRAINT accommodation_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.accommodation_type(id);


--
-- Name: activity_category activity_category_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_category
    ADD CONSTRAINT activity_category_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: activity activity_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity
    ADD CONSTRAINT activity_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.activity_category(id);


--
-- Name: activity activity_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity
    ADD CONSTRAINT activity_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: activity_opening_hours activity_opening_hours_activity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_opening_hours
    ADD CONSTRAINT activity_opening_hours_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activity(id) ON DELETE CASCADE;


--
-- Name: activity_tag_junction activity_tag_junction_activity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction
    ADD CONSTRAINT activity_tag_junction_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activity(id) ON DELETE CASCADE;


--
-- Name: activity_tag_junction activity_tag_junction_tag_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction
    ADD CONSTRAINT activity_tag_junction_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tag(id);


--
-- Name: amenity amenity_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.amenity
    ADD CONSTRAINT amenity_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: budget_allocation budget_allocation_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation
    ADD CONSTRAINT budget_allocation_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.budget_category(id);


--
-- Name: budget_allocation budget_allocation_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation
    ADD CONSTRAINT budget_allocation_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: budget_category budget_category_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_category
    ADD CONSTRAINT budget_category_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: car_rental car_rental_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT car_rental_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: car_rental car_rental_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT car_rental_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: car_rental car_rental_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT car_rental_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: city city_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.city
    ADD CONSTRAINT city_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id);


--
-- Name: country_custom country_custom_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_custom
    ADD CONSTRAINT country_custom_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON DELETE CASCADE;


--
-- Name: country_custom country_custom_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_custom
    ADD CONSTRAINT country_custom_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: country_emergency_contact country_emergency_contact_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_emergency_contact
    ADD CONSTRAINT country_emergency_contact_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON DELETE CASCADE;


--
-- Name: cuisine_type cuisine_type_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cuisine_type
    ADD CONSTRAINT cuisine_type_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: daily_itinerary_item daily_itinerary_item_daily_itinerary_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary_item
    ADD CONSTRAINT daily_itinerary_item_daily_itinerary_id_fkey FOREIGN KEY (daily_itinerary_id) REFERENCES public.daily_itinerary(id) ON DELETE CASCADE;


--
-- Name: daily_itinerary daily_itinerary_trip_day_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary
    ADD CONSTRAINT daily_itinerary_trip_day_id_fkey FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: entity_image entity_image_image_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_image
    ADD CONSTRAINT entity_image_image_id_fkey FOREIGN KEY (image_id) REFERENCES public.image(id) ON DELETE CASCADE;


--
-- Name: expense_allocation expense_allocation_expense_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense_allocation
    ADD CONSTRAINT expense_allocation_expense_id_fkey FOREIGN KEY (expense_id) REFERENCES public.expense(id) ON DELETE CASCADE;


--
-- Name: expense expense_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT expense_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.budget_category(id);


--
-- Name: expense expense_trip_day_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT expense_trip_day_id_fkey FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id);


--
-- Name: expense expense_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT expense_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: accommodation_amenity_junction fk_accommodation_amenity_junction_accommodation; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction
    ADD CONSTRAINT fk_accommodation_amenity_junction_accommodation FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation_amenity_junction fk_accommodation_amenity_junction_amenity; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_amenity_junction
    ADD CONSTRAINT fk_accommodation_amenity_junction_amenity FOREIGN KEY (amenity_id) REFERENCES public.amenity(id);


--
-- Name: accommodation fk_accommodation_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation
    ADD CONSTRAINT fk_accommodation_location FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: accommodation_price_forecast fk_accommodation_price_forecast_accommodation; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_price_forecast
    ADD CONSTRAINT fk_accommodation_price_forecast_accommodation FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation_rate fk_accommodation_rate_accommodation; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_rate
    ADD CONSTRAINT fk_accommodation_rate_accommodation FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation_rate fk_accommodation_rate_room; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_rate
    ADD CONSTRAINT fk_accommodation_rate_room FOREIGN KEY (room_id) REFERENCES public.accommodation_room(id) ON DELETE CASCADE;


--
-- Name: accommodation_room fk_accommodation_room_accommodation; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_room
    ADD CONSTRAINT fk_accommodation_room_accommodation FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;


--
-- Name: accommodation fk_accommodation_type; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation
    ADD CONSTRAINT fk_accommodation_type FOREIGN KEY (type_id) REFERENCES public.accommodation_type(id);


--
-- Name: accommodation_type fk_accommodation_type_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accommodation_type
    ADD CONSTRAINT fk_accommodation_type_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: activity fk_activity_category; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity
    ADD CONSTRAINT fk_activity_category FOREIGN KEY (category_id) REFERENCES public.activity_category(id);


--
-- Name: activity_category fk_activity_category_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_category
    ADD CONSTRAINT fk_activity_category_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: activity fk_activity_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity
    ADD CONSTRAINT fk_activity_location FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: activity_opening_hours fk_activity_opening_hours_activity; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_opening_hours
    ADD CONSTRAINT fk_activity_opening_hours_activity FOREIGN KEY (activity_id) REFERENCES public.activity(id) ON DELETE CASCADE;


--
-- Name: activity_tag_junction fk_activity_tag_junction_activity; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction
    ADD CONSTRAINT fk_activity_tag_junction_activity FOREIGN KEY (activity_id) REFERENCES public.activity(id) ON DELETE CASCADE;


--
-- Name: activity_tag_junction fk_activity_tag_junction_tag; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activity_tag_junction
    ADD CONSTRAINT fk_activity_tag_junction_tag FOREIGN KEY (tag_id) REFERENCES public.tag(id);


--
-- Name: amenity fk_amenity_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.amenity
    ADD CONSTRAINT fk_amenity_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: budget_allocation fk_budget_allocation_category; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation
    ADD CONSTRAINT fk_budget_allocation_category FOREIGN KEY (category_id) REFERENCES public.budget_category(id);


--
-- Name: budget_allocation fk_budget_allocation_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_allocation
    ADD CONSTRAINT fk_budget_allocation_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: budget_category fk_budget_category_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget_category
    ADD CONSTRAINT fk_budget_category_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: car_rental fk_car_rental_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT fk_car_rental_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: car_rental fk_car_rental_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT fk_car_rental_location FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: car_rental fk_car_rental_provider; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_rental
    ADD CONSTRAINT fk_car_rental_provider FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: city fk_city_country; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.city
    ADD CONSTRAINT fk_city_country FOREIGN KEY (country_id) REFERENCES public.country(id);


--
-- Name: country_custom fk_country_custom_country; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_custom
    ADD CONSTRAINT fk_country_custom_country FOREIGN KEY (country_id) REFERENCES public.country(id) ON DELETE CASCADE;


--
-- Name: country_custom fk_country_custom_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_custom
    ADD CONSTRAINT fk_country_custom_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: country_emergency_contact fk_country_emergency_contact_country; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.country_emergency_contact
    ADD CONSTRAINT fk_country_emergency_contact_country FOREIGN KEY (country_id) REFERENCES public.country(id) ON DELETE CASCADE;


--
-- Name: cuisine_type fk_cuisine_type_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cuisine_type
    ADD CONSTRAINT fk_cuisine_type_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: daily_itinerary_item fk_daily_itinerary_item_daily_itinerary; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary_item
    ADD CONSTRAINT fk_daily_itinerary_item_daily_itinerary FOREIGN KEY (daily_itinerary_id) REFERENCES public.daily_itinerary(id) ON DELETE CASCADE;


--
-- Name: daily_itinerary fk_daily_itinerary_trip_day; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.daily_itinerary
    ADD CONSTRAINT fk_daily_itinerary_trip_day FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: expense_allocation fk_expense_allocation_expense; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense_allocation
    ADD CONSTRAINT fk_expense_allocation_expense FOREIGN KEY (expense_id) REFERENCES public.expense(id) ON DELETE CASCADE;


--
-- Name: expense fk_expense_category; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT fk_expense_category FOREIGN KEY (category_id) REFERENCES public.budget_category(id);


--
-- Name: expense fk_expense_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT fk_expense_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: expense fk_expense_trip_day; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.expense
    ADD CONSTRAINT fk_expense_trip_day FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: guide_availability fk_guide_availability_guide; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_availability
    ADD CONSTRAINT fk_guide_availability_guide FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_language_junction fk_guide_language_junction_guide; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction
    ADD CONSTRAINT fk_guide_language_junction_guide FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_language_junction fk_guide_language_junction_language; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction
    ADD CONSTRAINT fk_guide_language_junction_language FOREIGN KEY (language_id) REFERENCES public.language(id);


--
-- Name: guide_location_junction fk_guide_location_junction_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction
    ADD CONSTRAINT fk_guide_location_junction_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: guide_location_junction fk_guide_location_junction_guide; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction
    ADD CONSTRAINT fk_guide_location_junction_guide FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_profile fk_guide_profile_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_profile
    ADD CONSTRAINT fk_guide_profile_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: guide_request fk_guide_request_guide; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT fk_guide_request_guide FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id);


--
-- Name: guide_request fk_guide_request_tour; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT fk_guide_request_tour FOREIGN KEY (tour_id) REFERENCES public.guided_tour(id);


--
-- Name: guide_request fk_guide_request_traveler_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT fk_guide_request_traveler_profile FOREIGN KEY (traveler_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: guide_request fk_guide_request_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT fk_guide_request_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id);


--
-- Name: guide_specialty fk_guide_specialty_guide; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_specialty
    ADD CONSTRAINT fk_guide_specialty_guide FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_specialty fk_guide_specialty_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_specialty
    ADD CONSTRAINT fk_guide_specialty_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: guided_tour fk_guided_tour_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guided_tour
    ADD CONSTRAINT fk_guided_tour_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: guided_tour fk_guided_tour_guide; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guided_tour
    ADD CONSTRAINT fk_guided_tour_guide FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: interest_category fk_interest_category_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.interest_category
    ADD CONSTRAINT fk_interest_category_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: line_station_junction fk_line_station_junction_line; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction
    ADD CONSTRAINT fk_line_station_junction_line FOREIGN KEY (line_id) REFERENCES public.transport_line(id);


--
-- Name: line_station_junction fk_line_station_junction_station; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction
    ADD CONSTRAINT fk_line_station_junction_station FOREIGN KEY (station_id) REFERENCES public.station(id);


--
-- Name: location fk_location_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.location
    ADD CONSTRAINT fk_location_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: meal_type fk_meal_type_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_type
    ADD CONSTRAINT fk_meal_type_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: notification fk_notification_sender_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT fk_notification_sender_profile FOREIGN KEY (sender_id) REFERENCES public.profile(id) ON DELETE SET NULL;


--
-- Name: notification fk_notification_user_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT fk_notification_user_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: restaurant_amenity_junction fk_restaurant_amenity_junction_amenity; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction
    ADD CONSTRAINT fk_restaurant_amenity_junction_amenity FOREIGN KEY (amenity_id) REFERENCES public.amenity(id);


--
-- Name: restaurant_amenity_junction fk_restaurant_amenity_junction_restaurant; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction
    ADD CONSTRAINT fk_restaurant_amenity_junction_restaurant FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id) ON DELETE CASCADE;


--
-- Name: restaurant_cuisine_junction fk_restaurant_cuisine_junction_cuisine; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction
    ADD CONSTRAINT fk_restaurant_cuisine_junction_cuisine FOREIGN KEY (cuisine_id) REFERENCES public.cuisine_type(id);


--
-- Name: restaurant_cuisine_junction fk_restaurant_cuisine_junction_restaurant; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction
    ADD CONSTRAINT fk_restaurant_cuisine_junction_restaurant FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id) ON DELETE CASCADE;


--
-- Name: restaurant fk_restaurant_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant
    ADD CONSTRAINT fk_restaurant_location FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: restaurant_opening_hours fk_restaurant_opening_hours_restaurant; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_opening_hours
    ADD CONSTRAINT fk_restaurant_opening_hours_restaurant FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id) ON DELETE CASCADE;


--
-- Name: restaurant fk_restaurant_type; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant
    ADD CONSTRAINT fk_restaurant_type FOREIGN KEY (type_id) REFERENCES public.restaurant_type(id);


--
-- Name: restaurant_type fk_restaurant_type_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_type
    ADD CONSTRAINT fk_restaurant_type_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: review_aspect_rating fk_review_aspect_rating_review; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.review_aspect_rating
    ADD CONSTRAINT fk_review_aspect_rating_review FOREIGN KEY (review_id) REFERENCES public.review(id) ON DELETE CASCADE;


--
-- Name: review_entry fk_review_entry_review; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.review_entry
    ADD CONSTRAINT fk_review_entry_review FOREIGN KEY (review_id) REFERENCES public.review(id) ON DELETE CASCADE;


--
-- Name: saved_item fk_saved_item_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.saved_item
    ADD CONSTRAINT fk_saved_item_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: station fk_station_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station
    ADD CONSTRAINT fk_station_location FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: station_transfer fk_station_transfer_from_station; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer
    ADD CONSTRAINT fk_station_transfer_from_station FOREIGN KEY (from_station_id) REFERENCES public.station(id);


--
-- Name: station_transfer fk_station_transfer_to_station; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer
    ADD CONSTRAINT fk_station_transfer_to_station FOREIGN KEY (to_station_id) REFERENCES public.station(id);


--
-- Name: tag fk_tag_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT fk_tag_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: taxi_fare fk_taxi_fare_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.taxi_fare
    ADD CONSTRAINT fk_taxi_fare_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: taxi_fare fk_taxi_fare_provider; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.taxi_fare
    ADD CONSTRAINT fk_taxi_fare_provider FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: tour_highlight fk_tour_highlight_tour; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_highlight
    ADD CONSTRAINT fk_tour_highlight_tour FOREIGN KEY (tour_id) REFERENCES public.guided_tour(id) ON DELETE CASCADE;


--
-- Name: tour_itinerary_point fk_tour_itinerary_point_tour; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_itinerary_point
    ADD CONSTRAINT fk_tour_itinerary_point_tour FOREIGN KEY (tour_id) REFERENCES public.guided_tour(id) ON DELETE CASCADE;


--
-- Name: transport_fare fk_transport_fare_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT fk_transport_fare_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: transport_fare fk_transport_fare_from_station; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT fk_transport_fare_from_station FOREIGN KEY (from_station_id) REFERENCES public.station(id);


--
-- Name: transport_fare fk_transport_fare_line; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT fk_transport_fare_line FOREIGN KEY (line_id) REFERENCES public.transport_line(id);


--
-- Name: transport_fare fk_transport_fare_mode; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT fk_transport_fare_mode FOREIGN KEY (mode_id) REFERENCES public.transport_mode(id);


--
-- Name: transport_fare fk_transport_fare_provider; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT fk_transport_fare_provider FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: transport_fare fk_transport_fare_to_station; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT fk_transport_fare_to_station FOREIGN KEY (to_station_id) REFERENCES public.station(id);


--
-- Name: transport_line fk_transport_line_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT fk_transport_line_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: transport_line fk_transport_line_mode; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT fk_transport_line_mode FOREIGN KEY (mode_id) REFERENCES public.transport_mode(id);


--
-- Name: transport_line fk_transport_line_provider; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT fk_transport_line_provider FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: transport_mode fk_transport_mode_icon; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_mode
    ADD CONSTRAINT fk_transport_mode_icon FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: trip_accommodation fk_trip_accommodation_accommodation; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT fk_trip_accommodation_accommodation FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id);


--
-- Name: trip_accommodation fk_trip_accommodation_room; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT fk_trip_accommodation_room FOREIGN KEY (room_id) REFERENCES public.accommodation_room(id);


--
-- Name: trip_accommodation fk_trip_accommodation_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT fk_trip_accommodation_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_accommodation fk_trip_accommodation_trip_day; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT fk_trip_accommodation_trip_day FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: trip_activity fk_trip_activity_activity; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_activity
    ADD CONSTRAINT fk_trip_activity_activity FOREIGN KEY (activity_id) REFERENCES public.activity(id);


--
-- Name: trip_activity fk_trip_activity_trip_day; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_activity
    ADD CONSTRAINT fk_trip_activity_trip_day FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: trip_change_log fk_trip_change_log_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_change_log
    ADD CONSTRAINT fk_trip_change_log_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: trip_change_log fk_trip_change_log_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_change_log
    ADD CONSTRAINT fk_trip_change_log_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_day fk_trip_day_city; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT fk_trip_day_city FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: trip_day fk_trip_day_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT fk_trip_day_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_interest_junction fk_trip_interest_junction_interest; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction
    ADD CONSTRAINT fk_trip_interest_junction_interest FOREIGN KEY (interest_id) REFERENCES public.interest_category(id);


--
-- Name: trip_interest_junction fk_trip_interest_junction_trip; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction
    ADD CONSTRAINT fk_trip_interest_junction_trip FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_meal fk_trip_meal_meal_type; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT fk_trip_meal_meal_type FOREIGN KEY (meal_type_id) REFERENCES public.meal_type(id);


--
-- Name: trip_meal fk_trip_meal_restaurant; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT fk_trip_meal_restaurant FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id);


--
-- Name: trip_meal fk_trip_meal_trip_day; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT fk_trip_meal_trip_day FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: trip fk_trip_origin_airport; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip
    ADD CONSTRAINT fk_trip_origin_airport FOREIGN KEY (origin_airport_id) REFERENCES public.station(id);


--
-- Name: trip fk_trip_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip
    ADD CONSTRAINT fk_trip_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: trip_transport fk_trip_transport_fare; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_fare FOREIGN KEY (fare_id) REFERENCES public.transport_fare(id);


--
-- Name: trip_transport fk_trip_transport_from_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_from_location FOREIGN KEY (from_location_id) REFERENCES public.location(id);


--
-- Name: trip_transport fk_trip_transport_line; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_line FOREIGN KEY (line_id) REFERENCES public.transport_line(id);


--
-- Name: trip_transport fk_trip_transport_mode; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_mode FOREIGN KEY (mode_id) REFERENCES public.transport_mode(id);


--
-- Name: trip_transport fk_trip_transport_provider; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_provider FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: trip_transport fk_trip_transport_to_location; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_to_location FOREIGN KEY (to_location_id) REFERENCES public.location(id);


--
-- Name: trip_transport fk_trip_transport_trip_day; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT fk_trip_transport_trip_day FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: user_interest_junction fk_user_interest_junction_interest; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interest_junction
    ADD CONSTRAINT fk_user_interest_junction_interest FOREIGN KEY (interest_id) REFERENCES public.interest_category(id) ON DELETE CASCADE;


--
-- Name: user_interest_junction fk_user_interest_junction_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interest_junction
    ADD CONSTRAINT fk_user_interest_junction_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: user_system_preference fk_user_system_preference_profile; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_system_preference
    ADD CONSTRAINT fk_user_system_preference_profile FOREIGN KEY (user_id) REFERENCES public.profile(id) ON DELETE CASCADE;


--
-- Name: guide_availability guide_availability_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_availability
    ADD CONSTRAINT guide_availability_guide_id_fkey FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_language_junction guide_language_junction_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction
    ADD CONSTRAINT guide_language_junction_guide_id_fkey FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_language_junction guide_language_junction_language_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_language_junction
    ADD CONSTRAINT guide_language_junction_language_id_fkey FOREIGN KEY (language_id) REFERENCES public.language(id);


--
-- Name: guide_location_junction guide_location_junction_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction
    ADD CONSTRAINT guide_location_junction_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: guide_location_junction guide_location_junction_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_location_junction
    ADD CONSTRAINT guide_location_junction_guide_id_fkey FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_request guide_request_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT guide_request_guide_id_fkey FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id);


--
-- Name: guide_request guide_request_tour_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT guide_request_tour_id_fkey FOREIGN KEY (tour_id) REFERENCES public.guided_tour(id);


--
-- Name: guide_request guide_request_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_request
    ADD CONSTRAINT guide_request_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id);


--
-- Name: guide_specialty guide_specialty_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_specialty
    ADD CONSTRAINT guide_specialty_guide_id_fkey FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: guide_specialty guide_specialty_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guide_specialty
    ADD CONSTRAINT guide_specialty_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: guided_tour guided_tour_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guided_tour
    ADD CONSTRAINT guided_tour_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: guided_tour guided_tour_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.guided_tour
    ADD CONSTRAINT guided_tour_guide_id_fkey FOREIGN KEY (guide_id) REFERENCES public.guide_profile(id) ON DELETE CASCADE;


--
-- Name: interest_category interest_category_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.interest_category
    ADD CONSTRAINT interest_category_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: line_station_junction line_station_junction_line_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction
    ADD CONSTRAINT line_station_junction_line_id_fkey FOREIGN KEY (line_id) REFERENCES public.transport_line(id);


--
-- Name: line_station_junction line_station_junction_station_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.line_station_junction
    ADD CONSTRAINT line_station_junction_station_id_fkey FOREIGN KEY (station_id) REFERENCES public.station(id);


--
-- Name: location location_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.location
    ADD CONSTRAINT location_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: meal_type meal_type_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_type
    ADD CONSTRAINT meal_type_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: restaurant_amenity_junction restaurant_amenity_junction_amenity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction
    ADD CONSTRAINT restaurant_amenity_junction_amenity_id_fkey FOREIGN KEY (amenity_id) REFERENCES public.amenity(id);


--
-- Name: restaurant_amenity_junction restaurant_amenity_junction_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_amenity_junction
    ADD CONSTRAINT restaurant_amenity_junction_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id) ON DELETE CASCADE;


--
-- Name: restaurant_cuisine_junction restaurant_cuisine_junction_cuisine_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction
    ADD CONSTRAINT restaurant_cuisine_junction_cuisine_id_fkey FOREIGN KEY (cuisine_id) REFERENCES public.cuisine_type(id);


--
-- Name: restaurant_cuisine_junction restaurant_cuisine_junction_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_cuisine_junction
    ADD CONSTRAINT restaurant_cuisine_junction_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id) ON DELETE CASCADE;


--
-- Name: restaurant restaurant_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant
    ADD CONSTRAINT restaurant_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: restaurant_opening_hours restaurant_opening_hours_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_opening_hours
    ADD CONSTRAINT restaurant_opening_hours_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id) ON DELETE CASCADE;


--
-- Name: restaurant_type restaurant_type_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant_type
    ADD CONSTRAINT restaurant_type_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: restaurant restaurant_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.restaurant
    ADD CONSTRAINT restaurant_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.restaurant_type(id);


--
-- Name: station station_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station
    ADD CONSTRAINT station_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.location(id);


--
-- Name: station_transfer station_transfer_from_station_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer
    ADD CONSTRAINT station_transfer_from_station_id_fkey FOREIGN KEY (from_station_id) REFERENCES public.station(id);


--
-- Name: station_transfer station_transfer_to_station_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.station_transfer
    ADD CONSTRAINT station_transfer_to_station_id_fkey FOREIGN KEY (to_station_id) REFERENCES public.station(id);


--
-- Name: tag tag_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT tag_icon_id_fkey FOREIGN KEY (icon_id) REFERENCES public.icon(id);


--
-- Name: taxi_fare taxi_fare_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.taxi_fare
    ADD CONSTRAINT taxi_fare_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: taxi_fare taxi_fare_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.taxi_fare
    ADD CONSTRAINT taxi_fare_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: tour_highlight tour_highlight_tour_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_highlight
    ADD CONSTRAINT tour_highlight_tour_id_fkey FOREIGN KEY (tour_id) REFERENCES public.guided_tour(id) ON DELETE CASCADE;


--
-- Name: tour_itinerary_point tour_itinerary_point_tour_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tour_itinerary_point
    ADD CONSTRAINT tour_itinerary_point_tour_id_fkey FOREIGN KEY (tour_id) REFERENCES public.guided_tour(id) ON DELETE CASCADE;


--
-- Name: transport_fare transport_fare_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: transport_fare transport_fare_from_station_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_from_station_id_fkey FOREIGN KEY (from_station_id) REFERENCES public.station(id);


--
-- Name: transport_fare transport_fare_line_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_line_id_fkey FOREIGN KEY (line_id) REFERENCES public.transport_line(id);


--
-- Name: transport_fare transport_fare_mode_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_mode_id_fkey FOREIGN KEY (mode_id) REFERENCES public.transport_mode(id);


--
-- Name: transport_fare transport_fare_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: transport_fare transport_fare_to_station_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_fare
    ADD CONSTRAINT transport_fare_to_station_id_fkey FOREIGN KEY (to_station_id) REFERENCES public.station(id);


--
-- Name: transport_line transport_line_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT transport_line_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: transport_line transport_line_mode_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT transport_line_mode_id_fkey FOREIGN KEY (mode_id) REFERENCES public.transport_mode(id);


--
-- Name: transport_line transport_line_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_line
    ADD CONSTRAINT transport_line_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: trip_accommodation trip_accommodation_accommodation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT trip_accommodation_accommodation_id_fkey FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id);


--
-- Name: trip_accommodation trip_accommodation_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT trip_accommodation_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.accommodation_room(id);


--
-- Name: trip_accommodation trip_accommodation_trip_day_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT trip_accommodation_trip_day_id_fkey FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: trip_accommodation trip_accommodation_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_accommodation
    ADD CONSTRAINT trip_accommodation_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_activity trip_activity_activity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_activity
    ADD CONSTRAINT trip_activity_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activity(id);


--
-- Name: trip_activity trip_activity_trip_day_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_activity
    ADD CONSTRAINT trip_activity_trip_day_id_fkey FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: trip_change_log trip_change_log_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_change_log
    ADD CONSTRAINT trip_change_log_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_day trip_day_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT trip_day_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.city(id);


--
-- Name: trip_day trip_day_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_day
    ADD CONSTRAINT trip_day_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_interest_junction trip_interest_junction_interest_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction
    ADD CONSTRAINT trip_interest_junction_interest_id_fkey FOREIGN KEY (interest_id) REFERENCES public.interest_category(id);


--
-- Name: trip_interest_junction trip_interest_junction_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_interest_junction
    ADD CONSTRAINT trip_interest_junction_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trip(id) ON DELETE CASCADE;


--
-- Name: trip_meal trip_meal_meal_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT trip_meal_meal_type_id_fkey FOREIGN KEY (meal_type_id) REFERENCES public.meal_type(id);


--
-- Name: trip_meal trip_meal_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT trip_meal_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurant(id);


--
-- Name: trip_meal trip_meal_trip_day_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_meal
    ADD CONSTRAINT trip_meal_trip_day_id_fkey FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: trip trip_origin_airport_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip
    ADD CONSTRAINT trip_origin_airport_id_fkey FOREIGN KEY (origin_airport_id) REFERENCES public.station(id);


--
-- Name: trip_transport trip_transport_fare_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_fare_id_fkey FOREIGN KEY (fare_id) REFERENCES public.transport_fare(id);


--
-- Name: trip_transport trip_transport_from_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_from_location_id_fkey FOREIGN KEY (from_location_id) REFERENCES public.location(id);


--
-- Name: trip_transport trip_transport_line_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_line_id_fkey FOREIGN KEY (line_id) REFERENCES public.transport_line(id);


--
-- Name: trip_transport trip_transport_mode_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_mode_id_fkey FOREIGN KEY (mode_id) REFERENCES public.transport_mode(id);


--
-- Name: trip_transport trip_transport_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.transport_provider(id);


--
-- Name: trip_transport trip_transport_to_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_to_location_id_fkey FOREIGN KEY (to_location_id) REFERENCES public.location(id);


--
-- Name: trip_transport trip_transport_trip_day_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.trip_transport
    ADD CONSTRAINT trip_transport_trip_day_id_fkey FOREIGN KEY (trip_day_id) REFERENCES public.trip_day(id) ON DELETE CASCADE;


--
-- Name: user_interest_junction user_interest_junction_interest_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interest_junction
    ADD CONSTRAINT user_interest_junction_interest_id_fkey FOREIGN KEY (interest_id) REFERENCES public.interest_category(id) ON DELETE CASCADE;


--
-- Name: guide_profile; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.guide_profile ENABLE ROW LEVEL SECURITY;

--
-- Name: guide_request; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.guide_request ENABLE ROW LEVEL SECURITY;

--
-- Name: notification; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.notification ENABLE ROW LEVEL SECURITY;

--
-- Name: profile; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.profile ENABLE ROW LEVEL SECURITY;

--
-- Name: saved_item; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.saved_item ENABLE ROW LEVEL SECURITY;

--
-- Name: trip; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.trip ENABLE ROW LEVEL SECURITY;

--
-- Name: trip_change_log; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.trip_change_log ENABLE ROW LEVEL SECURITY;

--
-- Name: user_interest_junction; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.user_interest_junction ENABLE ROW LEVEL SECURITY;

--
-- Name: user_system_preference; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.user_system_preference ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

