#!/usr/bin/env python3
"""
Test the orchestrator initialization and validation only
"""

import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from load_main import HotelDataOrchestrator

def test_orchestrator():
    """Test orchestrator initialization and validation"""
    print("🔍 TESTING ORCHESTRATOR INITIALIZATION")
    print("="*50)
    
    try:
        # Initialize orchestrator
        orchestrator = HotelDataOrchestrator()
        print("✅ Orchestrator initialized")
        
        # Setup logging
        logger = orchestrator.setup_logging()
        print("✅ Logging setup completed")
        
        # Initialize database manager
        from config import HotelLoadingConfig
        from database import DatabaseManager
        
        orchestrator.db_manager = DatabaseManager(HotelLoadingConfig.DATABASE_CONFIG)
        print("✅ Database manager initialized")
        
        # Test validation (without running actual loading)
        print("\n🔍 Testing prerequisite validation...")
        validation_result = orchestrator.validate_prerequisites()
        
        if validation_result:
            print("✅ All prerequisites validated successfully")
            print("🚀 System is ready for full execution")
        else:
            print("❌ Prerequisites validation failed")
            return False
        
        # Close database connection
        orchestrator.db_manager.close()
        
        print("\n🎉 ORCHESTRATOR TEST COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_orchestrator()
    sys.exit(0 if success else 1)
