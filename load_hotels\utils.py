#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Utilities
Shared utility functions for all phases.
"""

import os
import json
import logging
import re
from typing import Dict, Optional, List, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DataValidator:
    """Data validation utilities"""

    @staticmethod
    def validate_phone_number(phone: str) -> Optional[str]:
        """Validate and clean phone number"""
        if not phone or not isinstance(phone, str):
            return None

        # Remove extra whitespace
        phone = phone.strip()

        # Basic validation - should contain digits and common phone characters
        if not re.search(r'\d', phone):
            return None

        # Check length constraints (accommodation.phone is varchar(50))
        if len(phone) > 50:
            logger.warning(f"Phone number too long, truncating: {phone[:50]}...")
            phone = phone[:50]

        return phone

    @staticmethod
    def validate_website_url(website: str) -> Optional[str]:
        """Validate and clean website URL"""
        if not website or not isinstance(website, str):
            return None

        # Remove extra whitespace
        website = website.strip()

        # Basic URL validation
        if not (website.startswith('http://') or website.startswith('https://')):
            return None

        # Check if it's a reasonable URL (contains domain-like structure)
        if not re.search(r'https?://[^\s/$.?#].[^\s]*', website):
            return None

        return website

    @staticmethod
    def validate_email(email: str) -> Optional[str]:
        """Validate email address"""
        if not email or not isinstance(email, str):
            return None

        email = email.strip()

        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, email):
            return email

        return None

    @staticmethod
    def combine_address_parts(street_address: str, postal_code: str) -> Optional[str]:
        """Combine street address and postal code into full address"""
        parts = []

        if street_address and isinstance(street_address, str):
            parts.append(street_address.strip())

        if postal_code and isinstance(postal_code, str):
            parts.append(postal_code.strip())

        if not parts:
            return None

        return ', '.join(parts)

    @staticmethod
    def validate_rating(rating: Any) -> Optional[float]:
        """Validate and convert rating to float"""
        if rating is None:
            return None

        try:
            rating_float = float(rating)
            # Ratings should be between 0 and 10
            if 0 <= rating_float <= 10:
                return rating_float
            else:
                logger.warning(f"Rating out of range (0-10): {rating_float}")
                return None
        except (ValueError, TypeError):
            logger.warning(f"Invalid rating format: {rating}")
            return None

class FileManager:
    """File management utilities"""

    @staticmethod
    def load_json_data(file_path: str) -> Dict:
        """Load JSON data from file with error handling"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"Data file not found: {file_path}")
                return {}

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"[OK] Loaded {len(data)} records from {file_path}")
                return data

        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return {}

    @staticmethod
    def get_directory_files(directory_path: str, extension: str = '.json') -> List[str]:
        """Get list of files in directory with specific extension"""
        try:
            if not os.path.exists(directory_path):
                logger.warning(f"Directory not found: {directory_path}")
                return []

            files = [f for f in os.listdir(directory_path) if f.endswith(extension)]
            logger.debug(f"Found {len(files)} {extension} files in {directory_path}")
            return files

        except Exception as e:
            logger.error(f"Error reading directory {directory_path}: {e}")
            return []

class ProgressTracker:
    """Progress tracking utilities"""

    def __init__(self, total_items: int, batch_size: int = 50):
        self.total_items = total_items
        self.batch_size = batch_size
        self.processed_items = 0
        self.start_time = datetime.now()

    def update(self, increment: int = 1):
        """Update progress counter"""
        self.processed_items += increment

        # Report progress at batch intervals
        if self.processed_items % self.batch_size == 0 or self.processed_items == self.total_items:
            self.report_progress()

    def report_progress(self):
        """Report current progress"""
        elapsed = datetime.now() - self.start_time
        percentage = (self.processed_items / self.total_items) * 100

        logger.info(f"  Progress: {self.processed_items}/{self.total_items} ({percentage:.1f}%) - "
                   f"Elapsed: {elapsed}")

    def get_final_stats(self) -> Dict:
        """Get final processing statistics"""
        total_time = datetime.now() - self.start_time
        items_per_second = self.processed_items / total_time.total_seconds() if total_time.total_seconds() > 0 else 0

        return {
            'total_items': self.total_items,
            'processed_items': self.processed_items,
            'total_time': total_time,
            'items_per_second': round(items_per_second, 2),
            'success_rate': (self.processed_items / self.total_items) * 100 if self.total_items > 0 else 0
        }

class LoggingSetup:
    """Logging configuration utilities"""

    @staticmethod
    def safe_unicode_message(message: str) -> str:
        """Convert Unicode characters to safe alternatives for Windows console"""
        import sys

        if sys.platform.startswith('win'):
            # Replace Unicode emojis with safe alternatives for Windows console
            replacements = {
                '✅': '[OK]',
                '❌': '[ERROR]',
                '⚠️': '[WARNING]',
                '🔍': '[SEARCH]',
                '🚀': '[START]',
                '⏭️': '[SKIP]',
                '📊': '[STATS]',
                '💾': '[SAVE]',
                '🔄': '[PROCESS]',
                '✨': '[SUCCESS]',
                '🎯': '[TARGET]',
                '📁': '[FILE]',
                '🌐': '[WEB]',
                '📞': '[PHONE]',
                '🏨': '[HOTEL]'
            }

            for unicode_char, replacement in replacements.items():
                message = message.replace(unicode_char, replacement)

        return message

    @staticmethod
    def setup_logging(log_file: str = 'hotel_loading.log', level: str = 'INFO'):
        """Setup logging configuration with proper UTF-8 encoding"""
        import sys

        log_level = getattr(logging, level.upper(), logging.INFO)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # Setup file handler with UTF-8 encoding
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)

        # Setup console handler with UTF-8 encoding for Windows compatibility
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)

        # Configure console encoding for Windows
        if sys.platform.startswith('win'):
            # Try to set console to UTF-8 mode
            try:
                import codecs
                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'replace')
                sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'replace')
            except (AttributeError, ImportError):
                # Fallback: Replace Unicode characters with safe alternatives
                pass

        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        return root_logger

class StatisticsCollector:
    """Statistics collection utilities"""

    def __init__(self):
        self.stats = {
            'start_time': datetime.now(),
            'phases': {},
            'cities': {},
            'overall': {
                'total_processed': 0,
                'total_errors': 0,
                'success_rate': 0.0
            }
        }

    def start_phase(self, phase_name: str):
        """Start tracking a phase"""
        self.stats['phases'][phase_name] = {
            'start_time': datetime.now(),
            'status': 'running',
            'processed': 0,
            'errors': 0,
            'cities': {}
        }

    def complete_phase(self, phase_name: str, success: bool = True):
        """Complete tracking a phase"""
        if phase_name in self.stats['phases']:
            phase_stats = self.stats['phases'][phase_name]
            phase_stats['end_time'] = datetime.now()
            phase_stats['duration'] = phase_stats['end_time'] - phase_stats['start_time']
            phase_stats['status'] = 'completed' if success else 'failed'

    def update_city_stats(self, phase_name: str, city_name: str, stats: Dict):
        """Update statistics for a city in a phase"""
        if phase_name in self.stats['phases']:
            self.stats['phases'][phase_name]['cities'][city_name] = stats

    def get_summary(self) -> Dict:
        """Get summary statistics"""
        end_time = datetime.now()
        total_duration = end_time - self.stats['start_time']

        # Calculate overall statistics
        total_processed = sum(
            phase_stats.get('processed', 0)
            for phase_stats in self.stats['phases'].values()
        )

        total_errors = sum(
            phase_stats.get('errors', 0)
            for phase_stats in self.stats['phases'].values()
        )

        success_rate = ((total_processed - total_errors) / total_processed * 100) if total_processed > 0 else 0

        self.stats['overall'].update({
            'end_time': end_time,
            'total_duration': total_duration,
            'total_processed': total_processed,
            'total_errors': total_errors,
            'success_rate': round(success_rate, 1)
        })

        return self.stats
