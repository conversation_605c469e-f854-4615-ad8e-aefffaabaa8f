-- ============================================================================
-- DUPLICATE DETECTION AND INCONSISTENCY ANALYSIS
-- Comprehensive queries to identify duplicate or inconsistent data
-- ============================================================================

\echo '============================================================================'
\echo 'HOTEL DATABASE VERIFICATION - DUPLICATE DETECTION'
\echo '============================================================================'
\echo ''

-- Duplicate External IDs
\echo '--- DUPLICATE EXTERNAL IDS ---'
SELECT 
    a.external_id,
    c.name as city_name,
    COUNT(*) as duplicate_count,
    STRING_AGG(a.name, ' | ' ORDER BY a.id) as accommodation_names,
    STRING_AGG(a.id::text, ', ' ORDER BY a.id) as accommodation_ids
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.external_id IS NOT NULL
GROUP BY a.external_id, c.id, c.name
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, c.name;

\echo ''

-- Identical Coordinates (Potential Duplicates)
\echo '--- ACCOMMODATIONS WITH IDENTICAL COORDINATES ---'
SELECT 
    l.lat,
    l.lng,
    c.name as city_name,
    COUNT(*) as accommodation_count,
    STRING_AGG(a.name, ' | ' ORDER BY a.id) as accommodation_names,
    STRING_AGG(a.external_id, ', ' ORDER BY a.id) as external_ids
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE l.lat IS NOT NULL AND l.lng IS NOT NULL
GROUP BY l.lat, l.lng, c.id, c.name
HAVING COUNT(*) > 1
ORDER BY accommodation_count DESC, c.name
LIMIT 20;

\echo ''

-- Similar Names (Potential Duplicates)
\echo '--- ACCOMMODATIONS WITH SIMILAR NAMES ---'
SELECT 
    a1.name as name1,
    a2.name as name2,
    a1.external_id as external_id1,
    a2.external_id as external_id2,
    c.name as city_name,
    ROUND(
        similarity(LOWER(a1.name), LOWER(a2.name))::numeric, 3
    ) as name_similarity
FROM accommodation a1
JOIN accommodation a2 ON a1.id < a2.id
JOIN location l1 ON a1.location_id = l1.id
JOIN location l2 ON a2.location_id = l2.id
JOIN city c ON l1.city_id = c.id AND l2.city_id = c.id
WHERE similarity(LOWER(a1.name), LOWER(a2.name)) > 0.7
  AND a1.name != a2.name
ORDER BY name_similarity DESC, c.name
LIMIT 15;

\echo ''

-- Duplicate Phone Numbers
\echo '--- DUPLICATE PHONE NUMBERS ---'
SELECT 
    a.phone,
    c.name as city_name,
    COUNT(*) as duplicate_count,
    STRING_AGG(a.name, ' | ' ORDER BY a.id) as accommodation_names,
    STRING_AGG(a.external_id, ', ' ORDER BY a.id) as external_ids
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.phone IS NOT NULL AND a.phone != ''
GROUP BY a.phone, c.id, c.name
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, c.name;

\echo ''

-- Duplicate Websites
\echo '--- DUPLICATE WEBSITES ---'
SELECT 
    a.website,
    c.name as city_name,
    COUNT(*) as duplicate_count,
    STRING_AGG(a.name, ' | ' ORDER BY a.id) as accommodation_names,
    STRING_AGG(a.external_id, ', ' ORDER BY a.id) as external_ids
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.website IS NOT NULL AND a.website != ''
GROUP BY a.website, c.id, c.name
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, c.name;

\echo ''

-- Inconsistent Star Ratings for Same External ID
\echo '--- INCONSISTENT STAR RATINGS FOR SAME EXTERNAL ID ---'
SELECT 
    a.external_id,
    c.name as city_name,
    COUNT(DISTINCT a.stars) as different_star_ratings,
    STRING_AGG(DISTINCT a.stars::text, ', ' ORDER BY a.stars) as star_ratings,
    STRING_AGG(a.name, ' | ' ORDER BY a.id) as accommodation_names
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.external_id IS NOT NULL AND a.stars IS NOT NULL
GROUP BY a.external_id, c.id, c.name
HAVING COUNT(DISTINCT a.stars) > 1
ORDER BY different_star_ratings DESC, c.name;

\echo ''

-- Locations with Too Many Accommodations (Suspicious)
\echo '--- LOCATIONS WITH SUSPICIOUS ACCOMMODATION COUNTS ---'
SELECT 
    l.id as location_id,
    l.lat,
    l.lng,
    l.address,
    c.name as city_name,
    COUNT(a.id) as accommodation_count,
    STRING_AGG(a.name, ' | ' ORDER BY a.id) as accommodation_names
FROM location l
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation a ON l.id = a.location_id
GROUP BY l.id, l.lat, l.lng, l.address, c.id, c.name
HAVING COUNT(a.id) > 5  -- More than 5 accommodations at exact same location
ORDER BY accommodation_count DESC, c.name;

\echo ''

-- Amenity Mapping Inconsistencies
\echo '--- AMENITY MAPPING INCONSISTENCIES ---'
-- Check for accommodations with same external_id but different amenities
WITH external_id_amenities AS (
    SELECT 
        a.external_id,
        c.name as city_name,
        a.id as accommodation_id,
        a.name as accommodation_name,
        COUNT(aaj.amenity_id) as amenity_count,
        STRING_AGG(am.name, ', ' ORDER BY am.name) as amenities
    FROM accommodation a
    JOIN location l ON a.location_id = l.id
    JOIN city c ON l.city_id = c.id
    LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.accommodation_id
    LEFT JOIN amenity am ON aaj.amenity_id = am.id
    WHERE a.external_id IS NOT NULL
    GROUP BY a.external_id, c.id, c.name, a.id, a.name
)
SELECT 
    external_id,
    city_name,
    COUNT(*) as accommodation_count,
    COUNT(DISTINCT amenity_count) as different_amenity_counts,
    STRING_AGG(DISTINCT amenity_count::text, ', ') as amenity_counts,
    STRING_AGG(accommodation_name, ' | ') as accommodation_names
FROM external_id_amenities
GROUP BY external_id, city_name
HAVING COUNT(*) > 1 AND COUNT(DISTINCT amenity_count) > 1
ORDER BY different_amenity_counts DESC, city_name;

\echo ''

-- Review Inconsistencies
\echo '--- REVIEW INCONSISTENCIES ---'
-- Check for accommodations with same external_id but different review counts
SELECT 
    a.external_id,
    c.name as city_name,
    COUNT(DISTINCT a.id) as accommodation_count,
    COUNT(DISTINCT r.id) as total_reviews,
    STRING_AGG(DISTINCT a.name, ' | ') as accommodation_names,
    STRING_AGG(
        DISTINCT (
            SELECT COUNT(*)::text 
            FROM review r2 
            WHERE r2.accommodation_id = a.id
        ), 
        ', '
    ) as reviews_per_accommodation
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN review r ON a.id = r.accommodation_id
WHERE a.external_id IS NOT NULL
GROUP BY a.external_id, c.id, c.name
HAVING COUNT(DISTINCT a.id) > 1
ORDER BY total_reviews DESC, c.name;

\echo ''

-- Data Loading Timestamp Inconsistencies
\echo '--- DATA LOADING TIMESTAMP INCONSISTENCIES ---'
SELECT 
    a.external_id,
    c.name as city_name,
    COUNT(DISTINCT DATE(a.created_at)) as different_load_dates,
    MIN(a.created_at) as first_loaded,
    MAX(a.created_at) as last_loaded,
    STRING_AGG(a.name, ' | ' ORDER BY a.created_at) as accommodation_names
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.external_id IS NOT NULL
GROUP BY a.external_id, c.id, c.name
HAVING COUNT(DISTINCT DATE(a.created_at)) > 1
ORDER BY different_load_dates DESC, c.name;

\echo ''
\echo '============================================================================'
\echo 'DUPLICATE DETECTION COMPLETED'
\echo '============================================================================'
