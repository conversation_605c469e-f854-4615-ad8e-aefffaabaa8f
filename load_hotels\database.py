#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Database Module
Shared database connection and utility functions for all phases.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging
from typing import Dict, Optional, List, Tuple
try:
    from .config import HotelLoadingConfig
except ImportError:
    from config import HotelLoadingConfig

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Centralized database connection and operations manager"""

    def __init__(self, config: Dict = None):
        self.config = config or HotelLoadingConfig.DATABASE_CONFIG
        self.connection = None
        self._city_ids_cache = {}

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.config)
            self.connection.autocommit = False
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Database connection closed")

    def get_cursor(self):
        """Get database cursor with RealDictCursor for named access"""
        if not self.connection:
            self.connect()
        return self.connection.cursor(cursor_factory=RealDictCursor)

    def test_connection(self) -> bool:
        """Test database connection and basic schema"""
        try:
            with self.get_cursor() as cursor:
                # Test basic connectivity
                cursor.execute("SELECT version()")
                version = cursor.fetchone()
                logger.info(f"✅ Database connected: {version['version'][:50]}...")

                # Test required tables exist
                required_tables = [
                    'accommodation', 'location', 'city', 'accommodation_type',
                    'amenity', 'accommodation_amenity_junction', 'review'
                ]

                for table in required_tables:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = %s
                        );
                    """, (table,))

                    exists = cursor.fetchone()['exists']
                    if not exists:
                        logger.error(f"❌ Required table '{table}' not found")
                        return False

                logger.info("✅ All required tables found")
                return True

        except Exception as e:
            logger.error(f"❌ Database test failed: {e}")
            return False

    def load_city_ids(self, target_cities: Dict) -> Dict:
        """Load city IDs from database and update configuration"""
        logger.info("Loading city IDs from database...")

        updated_cities = target_cities.copy()

        try:
            with self.get_cursor() as cursor:
                for city_key, city_config in updated_cities.items():
                    cursor.execute(
                        "SELECT id FROM city WHERE LOWER(name) = %s AND country_id = 1",
                        (city_config['city_name'].lower(),)
                    )
                    result = cursor.fetchone()

                    if result:
                        updated_cities[city_key]['city_id'] = result['id']
                        self._city_ids_cache[city_key] = result['id']
                        logger.info(f"✓ Found {city_config['city_name']} with ID {result['id']}")
                    else:
                        logger.error(f"✗ City '{city_config['city_name']}' not found in database")
                        raise ValueError(f"City '{city_config['city_name']}' not found")

            return updated_cities

        except Exception as e:
            logger.error(f"Failed to load city IDs: {e}")
            raise

    def get_accommodation_type_id(self, type_name: str = 'Hotel') -> int:
        """Get accommodation type ID"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("SELECT id FROM accommodation_type WHERE name = %s", (type_name,))
                result = cursor.fetchone()
                if result:
                    logger.debug(f"✓ Found {type_name} accommodation type with ID {result['id']}")
                    return result['id']
                else:
                    raise Exception(f"{type_name} accommodation type not found in database")
        except Exception as e:
            logger.error(f"Failed to get {type_name} accommodation type: {e}")
            raise

    def get_accommodation_by_external_id(self, external_id: str, city_id: int) -> Optional[Tuple[int, int]]:
        """Get accommodation and location IDs by external ID"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("""
                    SELECT a.id as accommodation_id, a.location_id
                    FROM accommodation a
                    JOIN location l ON a.location_id = l.id
                    WHERE a.external_id = %s AND l.city_id = %s
                """, (external_id, city_id))

                result = cursor.fetchone()
                return (result['accommodation_id'], result['location_id']) if result else None

        except Exception as e:
            logger.error(f"Error getting accommodation by external ID {external_id}: {e}")
            return None

    def commit(self):
        """Commit current transaction"""
        if self.connection:
            self.connection.commit()

    def rollback(self):
        """Rollback current transaction"""
        if self.connection:
            self.connection.rollback()

    def get_database_stats(self) -> Dict:
        """Get current database statistics"""
        try:
            with self.get_cursor() as cursor:
                stats = {}

                # Count accommodations
                cursor.execute("SELECT COUNT(*) as count FROM accommodation")
                stats['total_accommodations'] = cursor.fetchone()['count']

                # Count accommodations with external_id
                cursor.execute("SELECT COUNT(*) as count FROM accommodation WHERE external_id IS NOT NULL")
                stats['accommodations_with_external_id'] = cursor.fetchone()['count']

                # Count accommodations with contact info
                cursor.execute("""
                    SELECT
                        COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as with_phone,
                        COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END) as with_website,
                        COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as with_address
                    FROM accommodation a
                    JOIN location l ON a.location_id = l.id
                """)
                contact_stats = cursor.fetchone()
                stats.update(contact_stats)

                # Count amenity mappings
                cursor.execute("SELECT COUNT(*) as count FROM accommodation_amenity_junction")
                stats['amenity_mappings'] = cursor.fetchone()['count']

                # Count reviews
                cursor.execute("SELECT COUNT(*) as count FROM review")
                stats['reviews'] = cursor.fetchone()['count']

                return stats

        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}

    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if exc_type:
            self.rollback()
        self.close()
