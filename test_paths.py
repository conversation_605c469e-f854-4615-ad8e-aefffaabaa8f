#!/usr/bin/env python3
"""
Test script to check file paths and encoding issues
"""

import os
import sys
sys.path.insert(0, 'load_hotels')

from load_hotels.config import HotelLoadingConfig

def test_file_paths():
    """Test file path resolution"""
    print("Current working directory:", os.getcwd())
    print("BASE_DATA_PATH:", HotelLoadingConfig.BASE_DATA_PATH)
    print("Absolute BASE_DATA_PATH:", os.path.abspath(HotelLoadingConfig.BASE_DATA_PATH))
    
    # Test file paths for agadir
    city_key = 'agadir'
    core_path = HotelLoadingConfig.get_data_file_path(city_key, 'core')
    contacts_path = HotelLoadingConfig.get_data_file_path(city_key, 'contacts')
    amenities_path = HotelLoadingConfig.get_data_file_path(city_key, 'amenities')
    reviews_path = HotelLoadingConfig.get_data_file_path(city_key, 'reviews')
    
    print(f'\nCore data path: {core_path}')
    print(f'Core data exists: {os.path.exists(core_path)}')
    print(f'Contacts path: {contacts_path}')
    print(f'Contacts exists: {os.path.exists(contacts_path)}')
    print(f'Amenities path: {amenities_path}')
    print(f'Amenities exists: {os.path.exists(amenities_path)}')
    print(f'Reviews path: {reviews_path}')
    print(f'Reviews exists: {os.path.exists(reviews_path)}')
    
    # Test validation
    print("\nValidation results:")
    validation_results = HotelLoadingConfig.validate_data_files()
    for city_key, city_results in validation_results.items():
        print(f"  {city_key}:")
        for data_type, exists in city_results.items():
            status = "✅" if exists else "❌"
            print(f"    {status} {data_type}")

def test_unicode_logging():
    """Test Unicode logging"""
    import logging
    
    # Test different encoding approaches
    print("\nTesting Unicode logging:")
    
    try:
        # Test 1: Direct print
        print("Direct print test: ✅ ❌")
        
        # Test 2: Logging with default setup
        logging.basicConfig(level=logging.INFO, format='%(message)s')
        logger = logging.getLogger(__name__)
        logger.info("Logging test: ✅ ❌")
        
    except UnicodeEncodeError as e:
        print(f"Unicode error: {e}")
        print("Need to fix console encoding")

if __name__ == "__main__":
    test_file_paths()
    test_unicode_logging()
