#!/usr/bin/env python3
"""
Test script for complete Phase 3 functionality
Tests external ID tracking, price forecasts, and image loading
"""

import sys
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from decimal import Decimal

def test_phase3_complete():
    """Test all Phase 3 functionality"""
    
    # Database configuration - UPDATE THESE VALUES
    DB_CONFIG = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }
    
    print("🧪 TESTING COMPLETE PHASE 3 FUNCTIONALITY")
    print("="*70)
    
    try:
        # Connect to database
        connection = psycopg2.connect(**DB_CONFIG)
        connection.autocommit = False
        
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            
            # Test 1: Check if external_id field exists
            print("\n1. Testing external_id field...")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'accommodation' 
                AND column_name = 'external_id'
            """)
            external_id_column = cursor.fetchone()
            
            if external_id_column:
                print(f"   ✅ external_id field exists: {external_id_column['data_type']}")
            else:
                print("   ❌ external_id field missing! Run add_external_id_tracking.sql first")
                return False
            
            # Test 2: Check accommodation_price_forecast table
            print("\n2. Testing price forecast table...")
            cursor.execute("""
                SELECT COUNT(*) as count FROM accommodation_price_forecast
            """)
            price_count = cursor.fetchone()['count']
            print(f"   📊 Current price forecasts: {price_count}")
            
            # Test 3: Check entity_image table
            print("\n3. Testing image table...")
            cursor.execute("""
                SELECT COUNT(*) as count FROM entity_image 
                WHERE entity_type = 'accommodation'
            """)
            image_count = cursor.fetchone()['count']
            print(f"   🖼️ Current accommodation images: {image_count}")
            
            # Test 4: Check accommodations with external IDs
            print("\n4. Testing external ID tracking...")
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(external_id) as with_external_id
                FROM accommodation
            """)
            external_stats = cursor.fetchone()
            print(f"   📋 Total accommodations: {external_stats['total']}")
            print(f"   🔗 With external IDs: {external_stats['with_external_id']}")
            
            # Test 5: Sample data analysis
            print("\n5. Sample data analysis...")
            cursor.execute("""
                SELECT 
                    a.id,
                    a.name,
                    a.external_id,
                    COUNT(apf.id) as price_forecasts,
                    COUNT(ei.id) as images
                FROM accommodation a
                LEFT JOIN accommodation_price_forecast apf ON a.id = apf.accommodation_id
                LEFT JOIN entity_image ei ON a.id = ei.entity_id AND ei.entity_type = 'accommodation'
                WHERE a.external_id IS NOT NULL
                GROUP BY a.id, a.name, a.external_id
                ORDER BY a.id
                LIMIT 5
            """)
            
            sample_data = cursor.fetchall()
            if sample_data:
                print("   📋 Sample accommodations with complete data:")
                for row in sample_data:
                    print(f"     • {row['name']} (ID: {row['id']}, External: {row['external_id']})")
                    print(f"       Price forecasts: {row['price_forecasts']}, Images: {row['images']}")
            else:
                print("   ⚠️ No accommodations with external IDs found")
            
            # Test 6: Check JSON data structure
            print("\n6. Testing JSON data structure...")
            try:
                with open('processed_data/casablanca.json', 'r', encoding='utf-8') as f:
                    city_data = json.load(f)
                
                # Get first hotel for testing
                first_hotel_key = list(city_data.keys())[0]
                first_hotel = city_data[first_hotel_key]
                
                print(f"   📄 Sample hotel data structure:")
                print(f"     • Hotel ID: {first_hotel.get('hotel_id')}")
                print(f"     • Name: {first_hotel['hotel_details'].get('name')}")
                print(f"     • Has price forecasts: {'forcasted_prices' in first_hotel['hotel_details']}")
                print(f"     • Has image: {'image_link' in first_hotel['hotel_details']}")
                
                # Check price forecast structure
                forecasts = first_hotel['hotel_details'].get('forcasted_prices', {})
                if forecasts:
                    print(f"     • Price forecast months: {list(forecasts.keys())}")
                    print(f"     • Sample prices: {list(forecasts.values())[:3]}")
                
            except FileNotFoundError:
                print("   ⚠️ casablanca.json not found - check data directory")
            except Exception as e:
                print(f"   ❌ Error reading JSON: {e}")
            
            # Test 7: Database constraints and relationships
            print("\n7. Testing database constraints...")
            
            # Check price forecast constraints
            cursor.execute("""
                SELECT constraint_name, constraint_type
                FROM information_schema.table_constraints
                WHERE table_name = 'accommodation_price_forecast'
                AND constraint_type = 'UNIQUE'
            """)
            price_constraints = cursor.fetchall()
            print(f"   🔒 Price forecast unique constraints: {len(price_constraints)}")
            
            # Check image constraints
            cursor.execute("""
                SELECT constraint_name, constraint_type
                FROM information_schema.table_constraints
                WHERE table_name = 'entity_image'
                AND constraint_type IN ('UNIQUE', 'CHECK')
            """)
            image_constraints = cursor.fetchall()
            print(f"   🔒 Entity image constraints: {len(image_constraints)}")
            
            print("\n" + "="*70)
            print("✅ PHASE 3 FUNCTIONALITY TEST COMPLETED")
            print("="*70)
            
            # Summary recommendations
            print("\n📋 SUMMARY & RECOMMENDATIONS:")
            
            if external_id_column:
                print("✅ External ID tracking: Ready")
            else:
                print("❌ External ID tracking: Run add_external_id_tracking.sql")
            
            if price_count > 0:
                print("✅ Price forecasts: Data loaded")
            else:
                print("⚠️ Price forecasts: No data (will be loaded by Phase 3)")
            
            if image_count > 0:
                print("✅ Images: Data loaded")
            else:
                print("⚠️ Images: No data (will be loaded by Phase 3)")
            
            print("\n🚀 NEXT STEPS:")
            print("1. Run add_external_id_tracking.sql if external_id field missing")
            print("2. Update database credentials in phase3_core_data_loader.py")
            print("3. Run: python phase3_core_data_loader.py")
            print("4. Verify all data loaded correctly")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    
    finally:
        if 'connection' in locals():
            connection.close()

def main():
    """Main test function"""
    
    print("Please update the DB_CONFIG in this script with your database credentials.")
    print("Then run this test to verify Phase 3 readiness.")
    
    # Uncomment the line below after updating DB_CONFIG
    success = test_phase3_complete()
    sys.exit(0 if success else 1)
    
    print("\nTo run the test:")
    print("1. Update DB_CONFIG with your database credentials")
    print("2. Uncomment the test_phase3_complete() call")
    print("3. Run: python test_phase3_complete.py")

if __name__ == "__main__":
    main()
