-- ============================================================================
-- DATA QUALITY CHECKS
-- Comprehensive validation of data integrity, constraints, and relationships
-- ============================================================================

\echo '============================================================================'
\echo 'HOTEL DATABASE VERIFICATION - DATA QUALITY CHECKS'
\echo '============================================================================'
\echo ''

-- Foreign Key Integrity Checks
\echo '--- FOREIGN KEY INTEGRITY CHECKS ---'

-- Check for accommodations with invalid location_id
\echo 'Accommodations with invalid location_id:'
SELECT COUNT(*) as invalid_location_refs
FROM accommodation a
LEFT JOIN location l ON a.location_id = l.id
WHERE l.id IS NULL;

-- Check for locations with invalid city_id
\echo 'Locations with invalid city_id:'
SELECT COUNT(*) as invalid_city_refs
FROM location l
LEFT JOIN city c ON l.city_id = c.id
WHERE c.id IS NULL;

-- Check for accommodations with invalid type_id
\echo 'Accommodations with invalid type_id:'
SELECT COUNT(*) as invalid_type_refs
FROM accommodation a
LEFT JOIN accommodation_type at ON a.type_id = at.id
WHERE at.id IS NULL;

\echo ''

-- Required Field Validation
\echo '--- REQUIRED FIELD VALIDATION ---'

-- Check for accommodations without names
SELECT 
    'Accommodations without names' as check_description,
    COUNT(*) as violation_count
FROM accommodation
WHERE name IS NULL OR name = ''
UNION ALL
-- Check for locations without city_id
SELECT 
    'Locations without city_id' as check_description,
    COUNT(*) as violation_count
FROM location
WHERE city_id IS NULL
UNION ALL
-- Check for accommodations without location_id
SELECT 
    'Accommodations without location_id' as check_description,
    COUNT(*) as violation_count
FROM accommodation
WHERE location_id IS NULL
UNION ALL
-- Check for accommodations without type_id
SELECT 
    'Accommodations without type_id' as check_description,
    COUNT(*) as violation_count
FROM accommodation
WHERE type_id IS NULL;

\echo ''

-- Data Range Validation
\echo '--- DATA RANGE VALIDATION ---'

-- Check for invalid star ratings
SELECT 
    'Invalid star ratings (not 1-5)' as check_description,
    COUNT(*) as violation_count
FROM accommodation
WHERE stars IS NOT NULL AND (stars < 1 OR stars > 5)
UNION ALL
-- Check for invalid average scores
SELECT 
    'Invalid average scores (not 0-10)' as check_description,
    COUNT(*) as violation_count
FROM accommodation
WHERE average_score IS NOT NULL AND (average_score < 0 OR average_score > 10)
UNION ALL
-- Check for invalid coordinates (Morocco bounds approximately)
SELECT 
    'Invalid coordinates (outside Morocco bounds)' as check_description,
    COUNT(*) as violation_count
FROM location
WHERE (lat IS NOT NULL AND lng IS NOT NULL) 
  AND (lat < 21.0 OR lat > 36.0 OR lng < -17.0 OR lng > -1.0);

\echo ''

-- Duplicate Detection
\echo '--- DUPLICATE DETECTION ---'

-- Check for duplicate external_ids within same city
\echo 'Duplicate external_ids within same city:'
SELECT 
    c.name as city_name,
    a.external_id,
    COUNT(*) as duplicate_count
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.external_id IS NOT NULL
GROUP BY c.id, c.name, a.external_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, c.name;

-- Check for accommodations with identical coordinates
\echo 'Accommodations with identical coordinates (potential duplicates):'
SELECT 
    l.lat,
    l.lng,
    COUNT(*) as accommodation_count,
    STRING_AGG(a.name, '; ') as accommodation_names
FROM accommodation a
JOIN location l ON a.location_id = l.id
WHERE l.lat IS NOT NULL AND l.lng IS NOT NULL
GROUP BY l.lat, l.lng
HAVING COUNT(*) > 3  -- More than 3 accommodations at exact same coordinates
ORDER BY accommodation_count DESC
LIMIT 10;

\echo ''

-- Orphaned Records Check
\echo '--- ORPHANED RECORDS CHECK ---'

-- Check for amenity mappings without valid accommodation
SELECT 
    'Orphaned amenity mappings' as check_description,
    COUNT(*) as orphaned_count
FROM accommodation_amenity_junction aaj
LEFT JOIN accommodation a ON aaj.accommodation_id = a.id
WHERE a.id IS NULL
UNION ALL
-- Check for reviews without valid accommodation
SELECT 
    'Orphaned reviews' as check_description,
    COUNT(*) as orphaned_count
FROM review r
LEFT JOIN accommodation a ON r.accommodation_id = a.id
WHERE a.id IS NULL
UNION ALL
-- Check for entity_images without valid accommodation
SELECT 
    'Orphaned entity images' as check_description,
    COUNT(*) as orphaned_count
FROM entity_image ei
LEFT JOIN accommodation a ON ei.entity_id = a.id
WHERE ei.entity_type = 'accommodation' AND a.id IS NULL;

\echo ''

-- Data Consistency Checks
\echo '--- DATA CONSISTENCY CHECKS ---'

-- Check for accommodations with amenities but no amenity mappings
SELECT 
    'Accommodations with highlights but no amenity mappings' as check_description,
    COUNT(*) as inconsistency_count
FROM accommodation a
WHERE a.highlights IS NOT NULL 
  AND array_length(a.highlights, 1) > 0
  AND NOT EXISTS (
      SELECT 1 FROM accommodation_amenity_junction aaj 
      WHERE aaj.accommodation_id = a.id
  );

-- Check for reviews with invalid ratings
SELECT 
    'Reviews with invalid ratings' as check_description,
    COUNT(*) as violation_count
FROM review
WHERE rating IS NOT NULL AND (rating < 1 OR rating > 10);

\echo ''
\echo '============================================================================'
\echo 'DATA QUALITY CHECKS COMPLETED'
\echo '============================================================================'
