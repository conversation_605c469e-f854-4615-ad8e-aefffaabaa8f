# Consolidated Hotel Data Loading System

A clean, production-ready hotel data loading system that consolidates and orchestrates all hotel data loading phases into a single, easy-to-use solution.

## 🚀 Quick Start

### Single Command Execution
```bash
cd load_hotels
python load_main.py
```

That's it! The system will automatically:
1. ✅ Validate database connection and data files
2. 🏨 Load core hotel data (hotels, locations, price forecasts, images)
3. 🛎️ Load amenities and reviews
4. 📞 Load contact information (phone, website, address)
5. 📊 Provide comprehensive progress reporting and statistics

## 📁 System Architecture

### File Structure
```
load_hotels/
├── __init__.py                    # Package initialization
├── config.py                     # Centralized configuration
├── database.py                   # Database connection management
├── utils.py                      # Shared utilities and validators
├── core_data_loader.py           # Phase 1: Core hotel data
├── amenities_reviews_loader.py   # Phase 2: Amenities & reviews
├── contacts_loader.py            # Phase 3: Contact information
├── load_main.py                  # Main orchestrator script
└── README.md                     # This documentation
```

### Key Components

#### 🔧 Configuration (`config.py`)
- **Centralized settings**: Database config, data paths, city configuration
- **Easy city management**: Add new cities by simply updating the `TARGET_CITIES` dictionary
- **Phase control**: Enable/disable individual phases as needed

#### 🗄️ Database Manager (`database.py`)
- **Shared connection handling**: Single database connection for all phases
- **Automatic city ID loading**: Dynamically loads city IDs from database
- **Connection testing**: Validates database connectivity and schema
- **Statistics tracking**: Provides before/after database statistics

#### 🛠️ Utilities (`utils.py`)
- **Data validation**: Phone numbers, URLs, emails, ratings
- **File management**: JSON loading with error handling
- **Progress tracking**: Real-time progress reporting
- **Logging setup**: Comprehensive logging configuration

## 🏙️ Adding New Cities

Adding a new city is incredibly simple:

1. **Update Configuration** (`config.py`):
```python
TARGET_CITIES = {
    'casablanca': {
        'city_name': 'Casablanca',
        'city_id': None,  # Auto-loaded from database
        'data_file': 'casablanca.json'
    },
    'marrakech': {
        'city_name': 'Marrakech',
        'city_id': None,  # Auto-loaded from database
        'data_file': 'marrakech.json'
    },
    # Add new city here:
    'agadir': {
        'city_name': 'Agadir',
        'city_id': None,  # Auto-loaded from database
        'data_file': 'agadir.json'
    }
}
```

2. **Ensure Data Files Exist**:
   - Core data: `../processed_data/agadir.json`
   - Contacts: `../processed_data/contacts/agadir.json`
   - Amenities: `../raw_data/amenities/agadir/`
   - Reviews: `../raw_data/reviews/agadir/`

3. **Run the System**: `python load_main.py`

The system will automatically process the new city through all phases!

## 📊 Processing Phases

### Phase 1: Core Data Loading
- **Hotels**: Basic hotel information and metadata
- **Locations**: Geographic coordinates and city associations
- **Price Forecasts**: Historical and predicted pricing data
- **Images**: Hotel image URLs and metadata
- **External ID Tracking**: Links for subsequent phases

### Phase 2: Amenities & Reviews Loading
- **Amenities**: Hotel facilities and services
- **Amenity Mappings**: Links between hotels and amenities
- **Reviews**: Customer reviews and ratings
- **Data Quality**: Validation and cleaning of review data

### Phase 3: Contact Information Loading
- **Phone Numbers**: Validated phone contact information
- **Websites**: Hotel website URLs
- **Addresses**: Combined street address and postal code
- **Contact Coverage**: Comprehensive contact data enrichment

## 🔒 Safety Features

### Database Safety
- **Individual transactions**: Each hotel processed independently
- **Automatic rollback**: Failed hotels don't affect others
- **Connection testing**: Validates database before starting
- **No data corruption**: Existing data is never overwritten unsafely

### Error Handling
- **Graceful failures**: System continues processing despite individual errors
- **Comprehensive logging**: Detailed error tracking and reporting
- **Progress preservation**: Completed work is saved even if later steps fail
- **Rollback mechanisms**: Failed operations are cleanly rolled back

### Data Validation
- **File existence**: Validates all required data files before starting
- **Schema validation**: Ensures database schema compatibility
- **Data quality**: Validates phone numbers, URLs, ratings, etc.
- **Referential integrity**: Maintains proper database relationships

## 📈 Monitoring and Reporting

### Real-time Progress
- **Phase-by-phase tracking**: Clear progress through each phase
- **City-by-city reporting**: Individual city processing status
- **Item-level progress**: Hotel-by-hotel processing updates
- **Error tracking**: Real-time error reporting and counting

### Final Statistics
- **Comprehensive summary**: Complete processing statistics
- **Success rates**: Phase and overall success percentages
- **Database impact**: Before/after database statistics
- **Performance metrics**: Processing speed and duration

### Logging
- **Timestamped logs**: Complete audit trail of all operations
- **Multiple log levels**: INFO, WARNING, ERROR for different detail levels
- **File and console**: Logs to both file and console simultaneously
- **Structured format**: Easy to parse and analyze

## 🔧 Configuration Options

### Database Configuration
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'database': 'testing_full_database',
    'user': 'postgres',
    'password': '1234',
    'port': 5432
}
```

### Phase Control
```python
PHASES = {
    'core_data': {'enabled': True},
    'amenities_reviews': {'enabled': True},
    'contacts': {'enabled': True}
}
```

### Processing Settings
```python
BATCH_SIZE = 50          # Progress reporting interval
MAX_RETRIES = 3          # Maximum retries for failed operations
LOG_LEVEL = 'INFO'       # Logging detail level
```

## 🎯 Benefits Over Original Implementation

### Code Quality
- **No duplication**: Shared database, logging, and utility functions
- **Clean architecture**: Modular design with clear separation of concerns
- **Consistent patterns**: Same error handling and transaction management across all phases
- **Easy maintenance**: Centralized configuration and utilities

### Usability
- **Single command**: `python load_main.py` runs everything
- **Automatic orchestration**: No manual phase coordination required
- **Easy expansion**: Add cities with simple configuration changes
- **Clear reporting**: Comprehensive progress and error reporting

### Reliability
- **Robust error handling**: Individual transaction handling preserved
- **Safety mechanisms**: Comprehensive rollback and validation
- **Data integrity**: Maintains all existing data quality safeguards
- **Production ready**: Suitable for automated deployment

## 🚀 Migration from Original Phases

The consolidated system maintains 100% compatibility with the original phase implementations:

- **Same database schema**: No changes to existing database structure
- **Same data quality**: Identical validation and processing logic
- **Same error handling**: Individual transaction handling preserved
- **Same scalability**: Easy city addition patterns maintained

### Original vs Consolidated
| Aspect | Original (5 separate scripts) | Consolidated (1 script) |
|--------|-------------------------------|-------------------------|
| **Execution** | Manual phase coordination | Single command |
| **Configuration** | Duplicated across phases | Centralized |
| **Database** | 5 separate connections | Shared connection |
| **Logging** | 5 separate log files | Unified logging |
| **Error Handling** | Inconsistent patterns | Standardized |
| **City Addition** | Update 5 files | Update 1 config |
| **Monitoring** | Manual aggregation | Automatic reporting |

## 🧪 Testing and Verification

### Database Verification Suite
Located in `database_verification/`, this comprehensive suite provides:
- **Basic count queries** for all tables and statistics
- **Data quality checks** for constraints and relationships
- **Sample data queries** to verify content accuracy
- **Duplicate detection** queries for data consistency
- **Performance tests** for common use cases

Run the complete verification suite:
```bash
cd database_verification
psql -h localhost -d hotel_db -U username -f run_all_tests.sql
```

Or run individual test files:
```bash
psql -h localhost -d hotel_db -U username -f 01_basic_counts.sql
psql -h localhost -d hotel_db -U username -f 02_data_quality.sql
psql -h localhost -d hotel_db -U username -f 03_sample_data.sql
psql -h localhost -d hotel_db -U username -f 04_duplicate_detection.sql
psql -h localhost -d hotel_db -U username -f 05_performance_tests.sql
```

### System Tests
- **Unit tests** for individual components
- **Integration tests** for end-to-end workflows
- **Performance benchmarks**

Run system tests:
```bash
python test_system.py
```

## 🧹 Project Cleanup

To clean up obsolete development files and maintain a clean project structure:

```bash
python cleanup_project.py
```

See `CLEANUP_ANALYSIS.md` for detailed cleanup recommendations.

## 📞 Support

For questions or issues:
1. Check the comprehensive logging output
2. Review the configuration in `config.py`
3. Validate data file existence and format
4. Ensure database connectivity and schema compatibility
5. Run the database verification suite to check data integrity

The system provides detailed error messages and suggestions for resolving common issues.
