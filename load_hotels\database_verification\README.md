# Database Verification and Testing Suite

This directory contains comprehensive SQL queries and tests to validate the hotel data loading system.

## Overview

The verification suite includes:
- **Count queries** for all tables
- **Data quality checks** for constraints and relationships
- **Sample data queries** to verify content accuracy
- **Duplicate detection** queries
- **Performance test** queries for common use cases

## How to Run Tests

### Prerequisites
- PostgreSQL client (psql) or database management tool
- Access to the hotel database
- Database connection parameters

### Running Individual Tests

```bash
# Run all basic count queries
psql -h localhost -d hotel_db -U username -f 01_basic_counts.sql

# Run data quality checks
psql -h localhost -d hotel_db -U username -f 02_data_quality.sql

# Run sample data verification
psql -h localhost -d hotel_db -U username -f 03_sample_data.sql

# Run duplicate detection
psql -h localhost -d hotel_db -U username -f 04_duplicate_detection.sql

# Run performance tests
psql -h localhost -d hotel_db -U username -f 05_performance_tests.sql
```

### Running All Tests
```bash
# Run the complete test suite
psql -h localhost -d hotel_db -U username -f run_all_tests.sql
```

## Expected Results

### Basic Counts (as of last verification)
- **Total accommodations**: 1,937
- **With external_id**: 1,937 (100%)
- **With phone**: 650 (33.5%)
- **With website**: 474 (24.5%)
- **With address**: 1,076 (55.5%)
- **Amenity mappings**: 56,211
- **Reviews**: 799

### Data Quality Expectations
- All accommodations should have valid location_id references
- All locations should have valid city_id references
- No NULL values in required fields
- All external_ids should be unique within city scope
- Coordinate values should be within valid ranges for Morocco

### Performance Expectations
- Basic accommodation queries: < 100ms
- Complex joins with amenities: < 500ms
- Geospatial queries: < 1000ms

## Test Files Description

1. **01_basic_counts.sql**: Basic table counts and statistics
2. **02_data_quality.sql**: Constraint validation and referential integrity
3. **03_sample_data.sql**: Sample data queries to verify content
4. **04_duplicate_detection.sql**: Detect duplicate or inconsistent data
5. **05_performance_tests.sql**: Performance benchmarks for common queries
6. **run_all_tests.sql**: Master script to run all tests

## Troubleshooting

If tests fail:
1. Check database connection parameters
2. Verify the hotel data loading system has been run
3. Check for schema changes or missing tables
4. Review error messages for specific issues

## Adding New Tests

To add new verification queries:
1. Create a new .sql file or add to existing files
2. Include comments explaining the test purpose
3. Add expected results to this README
4. Update run_all_tests.sql if needed
