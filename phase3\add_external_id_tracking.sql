-- =====================================================
-- EXTERNAL ID TRACKING ENHANCEMENT
-- Add external_id field to accommodation table for future phase linking
-- =====================================================

-- STEP 1: Check if external_id field already exists
SELECT 'Current Accommodation Table Structure' as analysis_type;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'accommodation' 
AND column_name = 'external_id';

-- STEP 2: Add external_id field if it doesn't exist
DO $$
BEGIN
    -- Check if column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'accommodation' 
        AND column_name = 'external_id'
    ) THEN
        -- Add the external_id column
        ALTER TABLE accommodation 
        ADD COLUMN external_id character varying(50);
        
        -- Add index for performance
        CREATE INDEX idx_accommodation_external_id 
        ON accommodation(external_id) 
        WHERE external_id IS NOT NULL;
        
        -- Add comment
        COMMENT ON COLUMN accommodation.external_id IS 'External ID from source system (Trivago, TripAdvisor, etc.)';
        
        RAISE NOTICE 'Added external_id column to accommodation table';
    ELSE
        RAISE NOTICE 'external_id column already exists in accommodation table';
    END IF;
END $$;

-- STEP 3: Verify the addition
SELECT 'Updated Accommodation Table Structure' as verification;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'accommodation' 
AND column_name = 'external_id';

-- STEP 4: Check indexes
SELECT 'External ID Indexes' as index_check;
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'accommodation' 
AND indexname LIKE '%external_id%';

-- =====================================================
-- ALTERNATIVE APPROACH: DEDICATED MAPPING TABLE
-- If you prefer a separate table for external ID mappings
-- =====================================================

-- Create external_id_mapping table (alternative approach)
CREATE TABLE IF NOT EXISTS external_id_mapping (
    id bigint GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    entity_type character varying(50) NOT NULL,
    entity_id bigint NOT NULL,
    external_id character varying(50) NOT NULL,
    source_system character varying(50) NOT NULL DEFAULT 'trivago',
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Constraints
    CONSTRAINT external_id_mapping_entity_type_check 
    CHECK (entity_type IN ('accommodation', 'restaurant', 'activity')),
    
    -- Unique constraint to prevent duplicates
    CONSTRAINT external_id_mapping_unique 
    UNIQUE (entity_type, entity_id, source_system),
    
    -- Unique external ID per source system
    CONSTRAINT external_id_mapping_external_unique 
    UNIQUE (external_id, source_system, entity_type)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_external_id_mapping_entity 
ON external_id_mapping(entity_type, entity_id);

CREATE INDEX IF NOT EXISTS idx_external_id_mapping_external 
ON external_id_mapping(external_id, source_system);

-- Add foreign key constraint for accommodations
ALTER TABLE external_id_mapping 
ADD CONSTRAINT fk_external_id_mapping_accommodation 
FOREIGN KEY (entity_id) 
REFERENCES accommodation(id) 
ON DELETE CASCADE
DEFERRABLE INITIALLY DEFERRED;

-- Add comments
COMMENT ON TABLE external_id_mapping IS 'Maps internal entity IDs to external system IDs';
COMMENT ON COLUMN external_id_mapping.entity_type IS 'Type of entity (accommodation, restaurant, activity)';
COMMENT ON COLUMN external_id_mapping.entity_id IS 'Internal database ID';
COMMENT ON COLUMN external_id_mapping.external_id IS 'External system ID (from Trivago, TripAdvisor, etc.)';
COMMENT ON COLUMN external_id_mapping.source_system IS 'Source system name (trivago, tripadvisor, etc.)';

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

-- Example 1: Insert external ID mapping
/*
INSERT INTO external_id_mapping (entity_type, entity_id, external_id, source_system)
VALUES ('accommodation', 123, '3059046', 'trivago');
*/

-- Example 2: Find accommodation by external ID
/*
SELECT a.* 
FROM accommodation a
JOIN external_id_mapping eim ON a.id = eim.entity_id
WHERE eim.external_id = '3059046' 
AND eim.source_system = 'trivago' 
AND eim.entity_type = 'accommodation';
*/

-- Example 3: Find external ID for accommodation
/*
SELECT eim.external_id, eim.source_system
FROM external_id_mapping eim
WHERE eim.entity_id = 123 
AND eim.entity_type = 'accommodation';
*/

-- =====================================================
-- RECOMMENDATION
-- =====================================================

SELECT 'RECOMMENDATION' as section;
SELECT 
    'APPROACH 1: Add external_id column to accommodation table' as option_1,
    'Simpler, direct approach for single source system' as option_1_pros,
    'Less flexible for multiple source systems' as option_1_cons,
    '' as separator,
    'APPROACH 2: Use external_id_mapping table' as option_2,
    'More flexible, supports multiple source systems' as option_2_pros,
    'Slightly more complex queries' as option_2_cons,
    '' as separator2,
    'RECOMMENDED: Use APPROACH 1 (external_id column) for now' as recommendation,
    'Can migrate to APPROACH 2 later if needed' as migration_note;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if external_id column was added successfully
SELECT 'Final Verification' as check_type;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'accommodation' 
            AND column_name = 'external_id'
        )
        THEN '✅ external_id column exists in accommodation table'
        ELSE '❌ external_id column NOT found in accommodation table'
    END as external_id_column_status,
    
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'external_id_mapping'
        )
        THEN '✅ external_id_mapping table exists'
        ELSE '❌ external_id_mapping table NOT found'
    END as mapping_table_status;

-- Show table sizes
SELECT 'Table Information' as info_type;
SELECT 
    'accommodation' as table_name,
    COUNT(*) as row_count
FROM accommodation
UNION ALL
SELECT 
    'external_id_mapping' as table_name,
    COUNT(*) as row_count
FROM external_id_mapping;
