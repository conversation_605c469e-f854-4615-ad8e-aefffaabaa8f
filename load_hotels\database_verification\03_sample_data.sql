-- ============================================================================
-- SAMPLE DATA VERIFICATION
-- Queries to verify content accuracy and data loading success
-- ============================================================================

\echo '============================================================================'
\echo 'HOTEL DATABASE VERIFICATION - SAMPLE DATA VERIFICATION'
\echo '============================================================================'
\echo ''

-- Sample Accommodations with Full Details
\echo '--- SAMPLE ACCOMMODATIONS WITH FULL DETAILS ---'
SELECT 
    a.id,
    a.name,
    a.external_id,
    a.stars,
    a.average_score,
    c.name as city_name,
    l.address,
    l.lat,
    l.lng,
    a.phone,
    a.website,
    CASE WHEN a.description IS NOT NULL THEN 'Yes' ELSE 'No' END as has_description,
    CASE WHEN a.highlights IS NOT NULL THEN array_length(a.highlights, 1) ELSE 0 END as highlight_count
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.external_id IS NOT NULL
ORDER BY a.id
LIMIT 10;

\echo ''

-- Sample Accommodations from Agadir (loaded data)
\echo '--- SAMPLE AGADIR ACCOMMODATIONS (LOADED DATA) ---'
SELECT 
    a.id,
    a.name,
    a.external_id,
    a.stars,
    a.average_score,
    l.address,
    a.phone,
    a.website,
    a.created_at
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name = 'Agadir'
  AND a.external_id IS NOT NULL
ORDER BY a.created_at DESC
LIMIT 15;

\echo ''

-- Accommodations with Most Amenities
\echo '--- ACCOMMODATIONS WITH MOST AMENITIES ---'
SELECT 
    a.name,
    c.name as city_name,
    COUNT(aaj.amenity_id) as amenity_count,
    STRING_AGG(am.name, ', ' ORDER BY am.name) as amenities
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.accommodation_id
LEFT JOIN amenity am ON aaj.amenity_id = am.id
GROUP BY a.id, a.name, c.name
HAVING COUNT(aaj.amenity_id) > 0
ORDER BY amenity_count DESC
LIMIT 10;

\echo ''

-- Sample Reviews with Details
\echo '--- SAMPLE REVIEWS WITH DETAILS ---'
SELECT 
    r.id,
    a.name as accommodation_name,
    c.name as city_name,
    r.rating,
    r.review_text,
    r.reviewer_name,
    r.review_date,
    r.external_id as review_external_id
FROM review r
JOIN accommodation a ON r.accommodation_id = a.id
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE r.review_text IS NOT NULL
ORDER BY r.review_date DESC
LIMIT 10;

\echo ''

-- Accommodations with Contact Information
\echo '--- ACCOMMODATIONS WITH COMPLETE CONTACT INFO ---'
SELECT 
    a.name,
    c.name as city_name,
    a.phone,
    a.website,
    a.email,
    l.address
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.phone IS NOT NULL 
  AND a.website IS NOT NULL 
  AND l.address IS NOT NULL
ORDER BY c.name, a.name
LIMIT 15;

\echo ''

-- Geographic Distribution Sample
\echo '--- GEOGRAPHIC DISTRIBUTION SAMPLE ---'
SELECT 
    c.name as city_name,
    ROUND(AVG(l.lat)::numeric, 4) as avg_latitude,
    ROUND(AVG(l.lng)::numeric, 4) as avg_longitude,
    ROUND(MIN(l.lat)::numeric, 4) as min_latitude,
    ROUND(MAX(l.lat)::numeric, 4) as max_latitude,
    ROUND(MIN(l.lng)::numeric, 4) as min_longitude,
    ROUND(MAX(l.lng)::numeric, 4) as max_longitude,
    COUNT(*) as accommodation_count
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE l.lat IS NOT NULL AND l.lng IS NOT NULL
GROUP BY c.id, c.name
ORDER BY accommodation_count DESC;

\echo ''

-- Recent Data Loading Activity
\echo '--- RECENT DATA LOADING ACTIVITY ---'
SELECT 
    DATE(a.created_at) as load_date,
    COUNT(*) as accommodations_loaded,
    COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) as with_external_id,
    COUNT(CASE WHEN a.phone IS NOT NULL THEN 1 END) as with_phone,
    COUNT(CASE WHEN a.website IS NOT NULL THEN 1 END) as with_website
FROM accommodation a
WHERE a.created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(a.created_at)
ORDER BY load_date DESC;

\echo ''

-- Sample Entity Images
\echo '--- SAMPLE ENTITY IMAGES ---'
SELECT 
    ei.id,
    ei.entity_type,
    a.name as accommodation_name,
    c.name as city_name,
    ei.image_url,
    ei.alt_text,
    ei.created_at
FROM entity_image ei
JOIN accommodation a ON ei.entity_id = a.id
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE ei.entity_type = 'accommodation'
ORDER BY ei.created_at DESC
LIMIT 10;

\echo ''

-- Data Quality Sample - Well-Populated Records
\echo '--- WELL-POPULATED ACCOMMODATION RECORDS ---'
SELECT 
    a.name,
    c.name as city_name,
    a.stars,
    a.average_score,
    CASE WHEN a.description IS NOT NULL THEN 'Yes' ELSE 'No' END as has_description,
    CASE WHEN a.phone IS NOT NULL THEN 'Yes' ELSE 'No' END as has_phone,
    CASE WHEN a.website IS NOT NULL THEN 'Yes' ELSE 'No' END as has_website,
    CASE WHEN l.address IS NOT NULL THEN 'Yes' ELSE 'No' END as has_address,
    (SELECT COUNT(*) FROM accommodation_amenity_junction WHERE accommodation_id = a.id) as amenity_count,
    (SELECT COUNT(*) FROM review WHERE accommodation_id = a.id) as review_count
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.stars IS NOT NULL
  AND a.average_score IS NOT NULL
  AND a.description IS NOT NULL
  AND (a.phone IS NOT NULL OR a.website IS NOT NULL)
  AND l.address IS NOT NULL
ORDER BY 
    (CASE WHEN a.phone IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN a.website IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN a.email IS NOT NULL THEN 1 ELSE 0 END) DESC,
    a.average_score DESC
LIMIT 10;

\echo ''
\echo '============================================================================'
\echo 'SAMPLE DATA VERIFICATION COMPLETED'
\echo '============================================================================'
