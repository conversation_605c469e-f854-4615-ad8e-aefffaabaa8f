-- ============================================================================
-- PERFORMANCE TESTS
-- Benchmark queries for common use cases and performance validation
-- ============================================================================

\echo '============================================================================'
\echo 'HOTEL DATABASE VERIFICATION - PERFORMANCE TESTS'
\echo '============================================================================'
\echo ''

-- Enable timing for performance measurement
\timing on

\echo '--- BASIC QUERY PERFORMANCE TESTS ---'

-- Test 1: Simple accommodation lookup by ID
\echo 'Test 1: Simple accommodation lookup by ID'
SELECT a.id, a.name, a.stars, a.average_score
FROM accommodation a
WHERE a.id = 1;

-- Test 2: Accommodation search by city
\echo 'Test 2: Accommodation search by city (Agadir)'
SELECT a.id, a.name, a.stars, a.average_score, l.address
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name = 'Agadir'
LIMIT 50;

-- Test 3: Accommodation search with star rating filter
\echo 'Test 3: Accommodation search with star rating filter (4+ stars)'
SELECT a.id, a.name, a.stars, a.average_score, c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.stars >= 4
ORDER BY a.average_score DESC
LIMIT 50;

\echo ''
\echo '--- COMPLEX JOIN PERFORMANCE TESTS ---'

-- Test 4: Accommodation with amenities (complex join)
\echo 'Test 4: Accommodations with amenities (complex join)'
SELECT 
    a.id,
    a.name,
    c.name as city_name,
    COUNT(aaj.amenity_id) as amenity_count,
    STRING_AGG(am.name, ', ' ORDER BY am.name) as amenities
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.accommodation_id
LEFT JOIN amenity am ON aaj.amenity_id = am.id
WHERE c.name = 'Agadir'
GROUP BY a.id, a.name, c.name
HAVING COUNT(aaj.amenity_id) > 5
ORDER BY amenity_count DESC
LIMIT 20;

-- Test 5: Accommodation with reviews and ratings
\echo 'Test 5: Accommodations with reviews and ratings'
SELECT 
    a.id,
    a.name,
    a.average_score,
    COUNT(r.id) as review_count,
    ROUND(AVG(r.rating)::numeric, 2) as avg_review_rating,
    c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN review r ON a.id = r.accommodation_id
GROUP BY a.id, a.name, a.average_score, c.name
HAVING COUNT(r.id) > 0
ORDER BY review_count DESC
LIMIT 30;

\echo ''
\echo '--- GEOSPATIAL QUERY PERFORMANCE TESTS ---'

-- Test 6: Nearby accommodations (distance calculation)
\echo 'Test 6: Accommodations near specific coordinates (Agadir center)'
SELECT 
    a.id,
    a.name,
    l.lat,
    l.lng,
    ROUND(
        (6371 * acos(
            cos(radians(30.4278)) * cos(radians(l.lat)) * 
            cos(radians(l.lng) - radians(-9.5981)) + 
            sin(radians(30.4278)) * sin(radians(l.lat))
        ))::numeric, 2
    ) as distance_km
FROM accommodation a
JOIN location l ON a.location_id = l.id
WHERE l.lat IS NOT NULL AND l.lng IS NOT NULL
  AND l.lat BETWEEN 30.3 AND 30.5
  AND l.lng BETWEEN -9.7 AND -9.5
ORDER BY distance_km
LIMIT 25;

-- Test 7: Accommodations within bounding box
\echo 'Test 7: Accommodations within geographic bounding box'
SELECT 
    a.id,
    a.name,
    l.lat,
    l.lng,
    c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE l.lat BETWEEN 30.4 AND 30.5
  AND l.lng BETWEEN -9.7 AND -9.5
ORDER BY l.lat, l.lng
LIMIT 50;

\echo ''
\echo '--- AGGREGATION PERFORMANCE TESTS ---'

-- Test 8: City statistics aggregation
\echo 'Test 8: City statistics aggregation'
SELECT 
    c.name as city_name,
    COUNT(a.id) as total_accommodations,
    COUNT(CASE WHEN a.stars >= 4 THEN 1 END) as high_rated_count,
    ROUND(AVG(a.average_score)::numeric, 2) as avg_score,
    COUNT(CASE WHEN a.phone IS NOT NULL THEN 1 END) as with_phone,
    COUNT(CASE WHEN a.website IS NOT NULL THEN 1 END) as with_website
FROM city c
LEFT JOIN location l ON c.id = l.city_id
LEFT JOIN accommodation a ON l.id = a.location_id
GROUP BY c.id, c.name
HAVING COUNT(a.id) > 0
ORDER BY total_accommodations DESC;

-- Test 9: Amenity popularity analysis
\echo 'Test 9: Amenity popularity analysis'
SELECT 
    am.name as amenity_name,
    COUNT(aaj.accommodation_id) as accommodation_count,
    ROUND(
        COUNT(aaj.accommodation_id) * 100.0 / 
        (SELECT COUNT(*) FROM accommodation)::numeric, 2
    ) as popularity_percentage
FROM amenity am
LEFT JOIN accommodation_amenity_junction aaj ON am.id = aaj.amenity_id
GROUP BY am.id, am.name
HAVING COUNT(aaj.accommodation_id) > 0
ORDER BY accommodation_count DESC
LIMIT 20;

\echo ''
\echo '--- SEARCH SIMULATION PERFORMANCE TESTS ---'

-- Test 10: Full-text search simulation
\echo 'Test 10: Full-text search simulation (hotel names containing "hotel")'
SELECT 
    a.id,
    a.name,
    a.stars,
    a.average_score,
    c.name as city_name,
    l.address
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE LOWER(a.name) LIKE '%hotel%'
ORDER BY a.average_score DESC NULLS LAST
LIMIT 30;

-- Test 11: Complex filter combination
\echo 'Test 11: Complex filter combination (4+ stars, with amenities, in Agadir)'
SELECT 
    a.id,
    a.name,
    a.stars,
    a.average_score,
    COUNT(aaj.amenity_id) as amenity_count,
    l.address
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.accommodation_id
WHERE c.name = 'Agadir'
  AND a.stars >= 4
  AND a.average_score IS NOT NULL
GROUP BY a.id, a.name, a.stars, a.average_score, l.address
HAVING COUNT(aaj.amenity_id) > 3
ORDER BY a.average_score DESC, amenity_count DESC
LIMIT 15;

-- Test 12: Recent data query
\echo 'Test 12: Recently loaded accommodations'
SELECT 
    a.id,
    a.name,
    a.external_id,
    c.name as city_name,
    a.created_at
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE a.created_at >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY a.created_at DESC
LIMIT 50;

\echo ''

-- Disable timing
\timing off

\echo '============================================================================'
\echo 'PERFORMANCE TESTS COMPLETED'
\echo 'Note: Review timing results above for performance analysis'
\echo 'Expected performance benchmarks:'
\echo '  - Basic queries: < 100ms'
\echo '  - Complex joins: < 500ms'
\echo '  - Geospatial queries: < 1000ms'
\echo '  - Aggregations: < 2000ms'
\echo '============================================================================'
